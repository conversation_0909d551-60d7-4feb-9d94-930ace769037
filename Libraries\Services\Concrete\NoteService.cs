using System.Linq;
using System.Threading.Tasks;
using Models.DbEntities;
using Data.UnitOfWork;

namespace Services.Concrete;

public class NoteService
{
    private readonly IUnitOfWork _unitOfWork;

    public NoteService(IUnitOfWork unitOfWork)
    {
        // Initialize UnitOfWork
        _unitOfWork = unitOfWork;
    }

    public async Task<Note> GetNoteByIdAsync(int id)
    {
        // Use UnitOfWork to fetch data
        var notes = await _unitOfWork.Repository<Note>().FindAllAsync(n => n.Id == id);
        return notes.FirstOrDefault();
    }
}