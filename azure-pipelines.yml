# Azure DevOps Pipeline for Notify Service API
# Supports CI/CD with Docker containerization and Azure deployment

trigger:
  branches:
    include:
      - main
      - develop
      - release/*
  paths:
    exclude:
      - README.md
      - docs/*
      - '*.md'

pr:
  branches:
    include:
      - main
      - develop
  paths:
    exclude:
      - README.md
      - docs/*
      - '*.md'

variables:
  # Build Configuration
  buildConfiguration: 'Release'
  dotNetFramework: 'net8.0'
  dotNetVersion: '8.0.x'
  
  # Docker Configuration
  dockerRegistryServiceConnection: 'notify-acr-connection'
  imageRepository: 'notify-service-api'
  containerRegistry: 'notifyregistry.azurecr.io'
  dockerfilePath: '$(Build.SourcesDirectory)/Dockerfile'
  tag: '$(Build.BuildId)'
  
  # Azure Configuration
  azureSubscription: 'notify-azure-subscription'
  resourceGroupName: 'notify-service-rg'
  webAppName: 'notify-service-api'
  
  # SonarCloud Configuration (Optional)
  sonarCloudServiceConnection: 'notify-sonarcloud'
  sonarCloudOrganization: 'your-org'
  sonarCloudProjectKey: 'notify-service-api'

stages:
- stage: Build
  displayName: 'Build and Test'
  jobs:
  - job: Build
    displayName: 'Build Job'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    # Checkout source code
    - checkout: self
      fetchDepth: 0  # Required for SonarCloud
    
    # Setup .NET SDK
    - task: UseDotNet@2
      displayName: 'Use .NET SDK $(dotNetVersion)'
      inputs:
        packageType: 'sdk'
        version: '$(dotNetVersion)'
        includePreviewVersions: false
    
    # Cache NuGet packages
    - task: Cache@2
      displayName: 'Cache NuGet packages'
      inputs:
        key: 'nuget | "$(Agent.OS)" | **/packages.lock.json,!**/bin/**,!**/obj/**'
        restoreKeys: |
          nuget | "$(Agent.OS)"
        path: '$(NUGET_PACKAGES)'
    
    # Restore dependencies
    - task: DotNetCoreCLI@2
      displayName: 'Restore NuGet packages'
      inputs:
        command: 'restore'
        projects: '**/*.csproj'
        feedsToUse: 'select'
        verbosityRestore: 'Minimal'
    
    # SonarCloud Prepare (Optional)
    - task: SonarCloudPrepare@1
      displayName: 'Prepare SonarCloud analysis'
      condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
      inputs:
        SonarCloud: '$(sonarCloudServiceConnection)'
        organization: '$(sonarCloudOrganization)'
        scannerMode: 'MSBuild'
        projectKey: '$(sonarCloudProjectKey)'
        projectName: 'Notify Service API'
        extraProperties: |
          sonar.exclusions=**/bin/**,**/obj/**,**/*.dll
          sonar.coverage.exclusions=**/Migrations/**,**/Program.cs,**/Startup.cs
    
    # Build solution
    - task: DotNetCoreCLI@2
      displayName: 'Build solution'
      inputs:
        command: 'build'
        projects: '**/*.csproj'
        arguments: '--configuration $(buildConfiguration) --no-restore'
    
    # Run unit tests
    - task: DotNetCoreCLI@2
      displayName: 'Run unit tests'
      inputs:
        command: 'test'
        projects: '**/UnitTests/*.csproj'
        arguments: '--configuration $(buildConfiguration) --no-build --collect:"XPlat Code Coverage" --logger trx --results-directory $(Agent.TempDirectory)'
        publishTestResults: true
    
    # Publish code coverage
    - task: PublishCodeCoverageResults@1
      displayName: 'Publish code coverage'
      inputs:
        codeCoverageTool: 'Cobertura'
        summaryFileLocation: '$(Agent.TempDirectory)/**/coverage.cobertura.xml'
    
    # SonarCloud Analyze (Optional)
    - task: SonarCloudAnalyze@1
      displayName: 'Run SonarCloud analysis'
      condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
    
    # SonarCloud Publish (Optional)
    - task: SonarCloudPublish@1
      displayName: 'Publish SonarCloud results'
      condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
      inputs:
        pollingTimeoutSec: '300'
    
    # Publish application
    - task: DotNetCoreCLI@2
      displayName: 'Publish application'
      inputs:
        command: 'publish'
        publishWebProjects: false
        projects: 'Presentations/WebApi/WebApi.csproj'
        arguments: '--configuration $(buildConfiguration) --output $(Build.ArtifactStagingDirectory)/app --no-build'
        zipAfterPublish: false
        modifyOutputPath: false
    
    # Copy Docker files
    - task: CopyFiles@2
      displayName: 'Copy Docker files'
      inputs:
        sourceFolder: '$(Build.SourcesDirectory)'
        contents: |
          Dockerfile
          docker-compose.yml
          docker-compose.override.yml
          .dockerignore
        targetFolder: '$(Build.ArtifactStagingDirectory)'
    
    # Copy deployment scripts
    - task: CopyFiles@2
      displayName: 'Copy deployment scripts'
      inputs:
        sourceFolder: '$(Build.SourcesDirectory)/scripts'
        contents: '**'
        targetFolder: '$(Build.ArtifactStagingDirectory)/scripts'
    
    # Publish build artifacts
    - task: PublishBuildArtifacts@1
      displayName: 'Publish build artifacts'
      inputs:
        pathToPublish: '$(Build.ArtifactStagingDirectory)'
        artifactName: 'drop'
        publishLocation: 'Container'

- stage: Docker
  displayName: 'Build and Push Docker Image'
  dependsOn: Build
  condition: and(succeeded(), or(eq(variables['Build.SourceBranch'], 'refs/heads/main'), eq(variables['Build.SourceBranch'], 'refs/heads/develop')))
  jobs:
  - job: Docker
    displayName: 'Docker Build and Push'
    pool:
      vmImage: 'ubuntu-latest'
    
    steps:
    # Download build artifacts
    - task: DownloadBuildArtifacts@0
      displayName: 'Download build artifacts'
      inputs:
        buildType: 'current'
        downloadType: 'single'
        artifactName: 'drop'
        downloadPath: '$(System.ArtifactsDirectory)'
    
    # Docker login
    - task: Docker@2
      displayName: 'Login to Container Registry'
      inputs:
        command: 'login'
        containerRegistry: '$(dockerRegistryServiceConnection)'
    
    # Build Docker image
    - task: Docker@2
      displayName: 'Build Docker image'
      inputs:
        command: 'build'
        repository: '$(imageRepository)'
        dockerfile: '$(dockerfilePath)'
        buildContext: '$(Build.SourcesDirectory)'
        tags: |
          $(tag)
          latest
        arguments: '--build-arg BUILD_CONFIGURATION=$(buildConfiguration)'
    
    # Push Docker image
    - task: Docker@2
      displayName: 'Push Docker image'
      inputs:
        command: 'push'
        repository: '$(imageRepository)'
        containerRegistry: '$(dockerRegistryServiceConnection)'
        tags: |
          $(tag)
          latest
    
    # Scan Docker image for vulnerabilities (Optional)
    - task: AquaSecurityTrivy@0
      displayName: 'Scan Docker image'
      inputs:
        image: '$(containerRegistry)/$(imageRepository):$(tag)'
        exitCode: 1
        severity: 'HIGH,CRITICAL'

- stage: DeployDev
  displayName: 'Deploy to Development'
  dependsOn: Docker
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/develop'))
  variables:
    environmentName: 'Development'
    webAppName: 'notify-service-api-dev'
  jobs:
  - deployment: DeployDev
    displayName: 'Deploy to Development'
    pool:
      vmImage: 'ubuntu-latest'
    environment: '$(environmentName)'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/deploy-steps.yml
            parameters:
              azureSubscription: '$(azureSubscription)'
              webAppName: '$(webAppName)'
              containerRegistry: '$(containerRegistry)'
              imageRepository: '$(imageRepository)'
              tag: '$(tag)'

- stage: DeployProd
  displayName: 'Deploy to Production'
  dependsOn: Docker
  condition: and(succeeded(), eq(variables['Build.SourceBranch'], 'refs/heads/main'))
  variables:
    environmentName: 'Production'
    webAppName: 'notify-service-api-prod'
  jobs:
  - deployment: DeployProd
    displayName: 'Deploy to Production'
    pool:
      vmImage: 'ubuntu-latest'
    environment: '$(environmentName)'
    strategy:
      runOnce:
        deploy:
          steps:
          - template: templates/deploy-steps.yml
            parameters:
              azureSubscription: '$(azureSubscription)'
              webAppName: '$(webAppName)'
              containerRegistry: '$(containerRegistry)'
              imageRepository: '$(imageRepository)'
              tag: '$(tag)'
