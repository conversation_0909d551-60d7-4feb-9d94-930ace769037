#nullable enable
using System;
using System.Collections.Generic;

namespace Models.DTOs.Notification;

/// <summary>
/// Universal notification send result
/// </summary>
public class NotificationResult
{
    /// <summary>
    /// Unique notification identifier
    /// </summary>
    public string NotificationId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the notification was processed successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Overall error message if processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Results per channel
    /// </summary>
    public Dictionary<string, ChannelResult> ChannelResults { get; set; } = new();

    /// <summary>
    /// Results per recipient
    /// </summary>
    public Dictionary<string, RecipientResult> RecipientResults { get; set; } = new();

    /// <summary>
    /// Notification type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Priority level
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Timestamp when the notification was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Timestamp when processing completed
    /// </summary>
    public DateTime? CompletedAt { get; set; }

    /// <summary>
    /// Total processing time in milliseconds
    /// </summary>
    public long? ProcessingTimeMs { get; set; }

    /// <summary>
    /// Tags associated with the notification
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Tracking information
    /// </summary>
    public NotificationTrackingResult? Tracking { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Result for a specific channel
/// </summary>
public class ChannelResult
{
    /// <summary>
    /// Channel name (email, sms, push, inapp, webhook)
    /// </summary>
    public string Channel { get; set; } = string.Empty;

    /// <summary>
    /// Whether the channel processing was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if channel processing failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of recipients processed
    /// </summary>
    public int RecipientsProcessed { get; set; }

    /// <summary>
    /// Number of successful deliveries
    /// </summary>
    public int SuccessfulDeliveries { get; set; }

    /// <summary>
    /// Number of failed deliveries
    /// </summary>
    public int FailedDeliveries { get; set; }

    /// <summary>
    /// Channel-specific message IDs
    /// </summary>
    public List<string>? MessageIds { get; set; }

    /// <summary>
    /// Processing time for this channel
    /// </summary>
    public long? ProcessingTimeMs { get; set; }
}

/// <summary>
/// Result for a specific recipient
/// </summary>
public class RecipientResult
{
    /// <summary>
    /// Recipient identifier
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Whether delivery to this recipient was successful
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if delivery failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Channels used for this recipient
    /// </summary>
    public List<string> Channels { get; set; } = new();

    /// <summary>
    /// Channel-specific results for this recipient
    /// </summary>
    public Dictionary<string, object>? ChannelDetails { get; set; }

    /// <summary>
    /// Delivery status
    /// </summary>
    public string Status { get; set; } = "pending";
}

/// <summary>
/// Notification tracking result
/// </summary>
public class NotificationTrackingResult
{
    /// <summary>
    /// Tracking ID
    /// </summary>
    public string? TrackingId { get; set; }

    /// <summary>
    /// Campaign ID
    /// </summary>
    public string? CampaignId { get; set; }

    /// <summary>
    /// Tracking URLs
    /// </summary>
    public Dictionary<string, string>? TrackingUrls { get; set; }

    /// <summary>
    /// Analytics properties
    /// </summary>
    public Dictionary<string, object>? AnalyticsProperties { get; set; }
}

/// <summary>
/// Universal notification schedule result
/// </summary>
public class NotificationScheduleResult
{
    /// <summary>
    /// Unique scheduled notification identifier
    /// </summary>
    public string ScheduledNotificationId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Number of recipients
    /// </summary>
    public int RecipientCount { get; set; }

    /// <summary>
    /// Channels to be used
    /// </summary>
    public List<string> Channels { get; set; } = new();

    /// <summary>
    /// Notification type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Priority level
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Whether the scheduled notification can be cancelled
    /// </summary>
    public bool CanCancel { get; set; } = true;
}
