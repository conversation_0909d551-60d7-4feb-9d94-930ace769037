#nullable enable
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;
using System;

namespace Models.DTOs.Push;

/// <summary>
/// Push notification payload
/// </summary>
public class PushPayload
{
    /// <summary>
    /// Device token or registration ID
    /// </summary>
    [Required]
    [StringLength(500)]
    public string DeviceToken { get; set; } = string.Empty;

    /// <summary>
    /// Notification title
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Notification body/message
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string Body { get; set; } = string.Empty;

    /// <summary>
    /// Optional notification icon
    /// </summary>
    [StringLength(200)]
    public string? Icon { get; set; }

    /// <summary>
    /// Optional notification image
    /// </summary>
    [StringLength(500)]
    public string? Image { get; set; }

    /// <summary>
    /// Optional click action URL
    /// </summary>
    [StringLength(500)]
    public string? ClickAction { get; set; }

    /// <summary>
    /// Optional notification sound
    /// </summary>
    [StringLength(100)]
    public string? Sound { get; set; }

    /// <summary>
    /// Optional badge count
    /// </summary>
    public int? Badge { get; set; }

    /// <summary>
    /// Custom data payload
    /// </summary>
    public Dictionary<string, string>? Data { get; set; }

    /// <summary>
    /// Notification priority (high, normal)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Time to live in seconds
    /// </summary>
    public int? TimeToLive { get; set; }

    /// <summary>
    /// Collapse key for message grouping
    /// </summary>
    [StringLength(100)]
    public string? CollapseKey { get; set; }

    /// <summary>
    /// Whether to send in dry run mode
    /// </summary>
    public bool DryRun { get; set; } = false;

    /// <summary>
    /// Target platform (android, ios, web)
    /// </summary>
    [StringLength(20)]
    public string? Platform { get; set; }

    /// <summary>
    /// Optional topic for topic-based messaging
    /// </summary>
    [StringLength(100)]
    public string? Topic { get; set; }

    /// <summary>
    /// Optional condition for conditional messaging
    /// </summary>
    [StringLength(500)]
    public string? Condition { get; set; }

  

}

