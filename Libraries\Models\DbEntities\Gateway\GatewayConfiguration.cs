using Models.DbEntities;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models.DbEntities.Gateway;

[Table("GatewayConfigurations")]
public class GatewayConfiguration : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string GatewayName { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string GatewayType { get; set; } = string.Empty; // Push, SMS, Email, MessengerApp, Webhook

    [Required]
    [MaxLength(50)]
    public string Provider { get; set; } = string.Empty; // FCM, Twilio, SendGrid, etc.

    [Column(TypeName = "jsonb")]
    public string ConfigurationData { get; set; } = "{}"; // JSON configuration

    [Column(TypeName = "jsonb")]
    public string RetryConfiguration { get; set; } = "{}"; // JSON retry settings

    public bool IsEnabled { get; set; } = true;

    public bool IsDefault { get; set; } = false;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    [MaxLength(100)]
    public string? UpdatedBy { get; set; }

    // Navigation properties
    public virtual ICollection<GatewayTemplate> Templates { get; set; } = new List<GatewayTemplate>();
    public virtual ICollection<GatewayLog> Logs { get; set; } = new List<GatewayLog>();
    public virtual ICollection<GatewayMetric> Metrics { get; set; } = new List<GatewayMetric>();
}
