using Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Models.DbEntities.Gateway;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Data.Repos.Gateway;

public class GatewayConfigurationRepository : GenericRepository<GatewayConfiguration>, IGatewayConfigurationRepository
{
    private readonly ApplicationDbContext _context;

    public GatewayConfigurationRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<GatewayConfiguration?> GetByGatewayNameAndProviderAsync(string gatewayName, string provider)
    {
        return await _context.Set<GatewayConfiguration>()
            .Include(x => x.Templates)
            .Include(x => x.Logs.Take(10))
            .Include(x => x.Metrics.Take(10))
            .FirstOrDefaultAsync(x => x.GatewayName == gatewayName && x.Provider == provider);
    }

    public async Task<List<GatewayConfiguration>> GetByGatewayTypeAsync(string gatewayType)
    {
        return await _context.Set<GatewayConfiguration>()
            .Include(x => x.Templates)
            .Where(x => x.GatewayType == gatewayType)
            .OrderBy(x => x.GatewayName)
            .ThenBy(x => x.Provider)
            .ToListAsync();
    }

    public async Task<List<GatewayConfiguration>> GetEnabledGatewaysAsync()
    {
        return await _context.Set<GatewayConfiguration>()
            .Include(x => x.Templates)
            .Where(x => x.IsEnabled)
            .OrderBy(x => x.GatewayType)
            .ThenBy(x => x.GatewayName)
            .ThenBy(x => x.Provider)
            .ToListAsync();
    }

    public async Task<GatewayConfiguration?> GetDefaultGatewayAsync(string gatewayType)
    {
        return await _context.Set<GatewayConfiguration>()
            .Include(x => x.Templates)
            .FirstOrDefaultAsync(x => x.GatewayType == gatewayType && x.IsDefault && x.IsEnabled);
    }

    public async Task<bool> SetDefaultGatewayAsync(int configurationId, string gatewayType)
    {
        using var transaction = await _context.Database.BeginTransactionAsync();
        try
        {
            // Remove default flag from all gateways of this type
            var existingDefaults = await _context.Set<GatewayConfiguration>()
                .Where(x => x.GatewayType == gatewayType && x.IsDefault)
                .ToListAsync();

            foreach (var config in existingDefaults)
            {
                config.IsDefault = false;
                config.UpdatedAt = DateTime.UtcNow;
            }

            // Set new default
            var newDefault = await _context.Set<GatewayConfiguration>()
                .FirstOrDefaultAsync(x => x.Id == configurationId);

            if (newDefault == null)
                return false;

            newDefault.IsDefault = true;
            newDefault.UpdatedAt = DateTime.UtcNow;

            await _context.SaveChangesAsync();
            await transaction.CommitAsync();
            return true;
        }
        catch
        {
            await transaction.RollbackAsync();
            return false;
        }
    }
}
