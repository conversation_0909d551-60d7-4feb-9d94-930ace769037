using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.Push;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Services.Gateway;

namespace Services.Gateways.Base
{
    public abstract class BasePushGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName) : BaseGateway(httpClient, dataService, gatewayName), IPushGateway, IAdminGateway, IMetricsGateway
    {
        public abstract string ProviderName { get; }
        public virtual bool IsEnabled => true;

        public virtual Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
            => Task.CompletedTask;

        public virtual Task<PushResult> SendWithTemplateAsync(string templateId, object templateData, string deviceToken, CancellationToken cancellationToken = default)
            => Task.FromResult(CreateErrorResult("Template sending not implemented"));

        public virtual Task<IReadOnlyList<PushResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string deviceToken)> recipients, CancellationToken cancellationToken = default)
            => Task.FromResult<IReadOnlyList<PushResult>>(
                recipients.Select(_ => CreateErrorResult("Bulk template sending not implemented")).ToList()
            );

        public virtual Task<PushSubscriptionResult> SubscribeToTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default)
            => Task.FromResult(new PushSubscriptionResult { IsSuccess = false, ErrorMessage = "Topic subscription not implemented" });

        public virtual Task<PushSubscriptionResult> UnsubscribeFromTopicAsync(string deviceToken, string topic, CancellationToken cancellationToken = default)
            => Task.FromResult(new PushSubscriptionResult { IsSuccess = false, ErrorMessage = "Topic unsubscription not implemented" });

        public abstract Task<PushResult> SendAsync(PushPayload payload, CancellationToken cancellationToken = default);
        public abstract Task<IReadOnlyList<PushResult>> SendBulkAsync(IEnumerable<PushPayload> payloads, CancellationToken cancellationToken = default);
        public abstract Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
        public abstract Task<PushScheduleResult> ScheduleMessageAsync(PushPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
        public abstract Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
        public abstract Task<PushScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, PushPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
        public abstract Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);
        public abstract GatewayCapabilities GetCapabilities();

        protected static PushResult CreateSuccessResult(string? messageId) => new()
        {
            MessageId = messageId ?? Guid.NewGuid().ToString(),
            IsSuccess = true,
            SentAt = DateTime.UtcNow
        };

        protected static PushResult CreateErrorResult(string errorMessage, string? messageId = null) => new()
        {
            MessageId = messageId ?? Guid.NewGuid().ToString(),
            IsSuccess = false,
            ErrorMessage = errorMessage,
            SentAt = DateTime.UtcNow
        };

        protected static PushScheduleResult CreateSuccessScheduleResult(string? scheduledMessageId, DateTimeOffset scheduledTime) => new()
        {
            ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
            IsScheduled = true,
            ScheduledTime = scheduledTime.DateTime
        };

        protected static PushScheduleResult CreateErrorScheduleResult(DateTimeOffset scheduledTime, string errorMessage, string? scheduledMessageId = null) => new()
        {
            ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
            IsScheduled = false,
            ErrorMessage = errorMessage,
            ScheduledTime = scheduledTime.DateTime
        };

        public virtual async Task<GatewayTestResult> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var testMessage = new PushPayload
                {
                    DeviceToken = "test_device_token",
                    Title = $"Test from {_gatewayName}",
                    Body = $"Test push notification from {_gatewayName} gateway"
                };

                var result = await SendAsync(testMessage, cancellationToken);
                return new GatewayTestResult
                {
                    IsSuccess = result.IsSuccess,
                    Message = result.IsSuccess ? $"{_gatewayName} gateway test successful" : $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    TestMessageId = result.MessageId,
                    TestDetails = new Dictionary<string, object> { ["Provider"] = _gatewayName, ["TestType"] = "LiveTest", ["DeviceToken"] = "test_device_token" },
                    ErrorMessage = result.ErrorMessage
                };
            }
            catch (Exception ex)
            {
                return new GatewayTestResult
                {
                    IsSuccess = false,
                    Message = $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    ErrorMessage = ex.Message,
                    TestDetails = new Dictionary<string, object> { ["Provider"] = _gatewayName, ["TestType"] = "LiveTest", ["Error"] = ex.Message }
                };
            }
        }

        public virtual async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> config, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveGatewayConfigurationAsync(_gatewayName, "push", ProviderName, config);
                return new GatewayConfigurationResult
                {
                    IsSuccess = true,
                    Configuration = config.ToDictionary<KeyValuePair<string, string>, string, object>(kvp => kvp.Key, kvp => kvp.Value),
                    GatewayName = _gatewayName,
                    LastUpdated = DateTime.UtcNow
                };
            }
            catch (Exception ex)
            {
                return new GatewayConfigurationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    LastUpdated = DateTime.UtcNow
                };
            }
        }

        public virtual async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
        {
            var config = await _dataService.GetGatewayConfigurationAsync(_gatewayName, ProviderName);
            var deserialized = config?.ConfigurationData != null
                ? System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(config.ConfigurationData) ?? new Dictionary<string, object>()
                : new Dictionary<string, object>();
            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Configuration = deserialized,
                GatewayName = _gatewayName,
                LastUpdated = config?.UpdatedAt
            };
        }

        public virtual async Task<GatewayTemplateResult> CreateTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveTemplateResultAsync(_gatewayName, ProviderName, template);
                return new GatewayTemplateResult
                {
                    IsSuccess = true,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual Task<GatewayTemplateResult> UpdateTemplateAsync(string templateId, GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            template.Id = templateId;
            return CreateTemplateAsync(template, cancellationToken);
        }

        public virtual async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _dataService.DeleteTemplateResultAsync(_gatewayName, ProviderName, templateId);
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await _dataService.GetTemplatesResultAsync(_gatewayName, ProviderName);
            }
            catch (Exception ex)
            {
                return new GatewayTemplatesResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName
                };
            }
        }

        public virtual Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
            => CreateTemplateAsync(template, cancellationToken);

        public virtual Task<GatewayRetryPolicyResult> SetRetryPolicyAsync(GatewayRetryPolicy policy, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayRetryPolicyResult
            {
                IsSuccess = true,
                RetryPolicy = policy,
                GatewayName = _gatewayName
            });

        public virtual Task<GatewayRetryPolicy> GetRetryPolicyAsync(CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayRetryPolicy
            {
                IsEnabled = true,
                MaxRetries = 3,
                InitialDelaySeconds = 1,
                MaxDelaySeconds = 60,
                BackoffMultiplier = 2.0,
                UseExponentialBackoff = true
            });

        public virtual Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayRetryConfiguration
            {
                RetryPolicy = new GatewayRetryPolicy
                {
                    IsEnabled = true,
                    MaxRetries = 3,
                    InitialDelaySeconds = 5,
                    MaxDelaySeconds = 300,
                    BackoffMultiplier = 2.0,
                    UseExponentialBackoff = true,
                    RetryableStatusCodes = [429, 500, 502, 503, 504],
                    RetryableExceptions = ["RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE"]
                },
                CircuitBreakerSettings = new(),
                RateLimitSettings = new()
            });

        public virtual Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayConfigurationResult
            {
                IsSuccess = true,
                Configuration = new Dictionary<string, object> { ["RetryConfiguration"] = retryConfig },
                GatewayName = _gatewayName,
                LastUpdated = DateTime.UtcNow
            });

        public virtual Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
            => TestConnectionAsync(cancellationToken);

        public virtual Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayUsageMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalMessages = 0,
                TotalBulkOperations = 0,
                TotalScheduledMessages = 0,
                AverageMessagesPerDay = 0,
                PeakMessagesPerHour = 0
            });

        public virtual Task<GatewayPerformanceMetrics> GetPerformanceMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayPerformanceMetrics
            {
                AverageResponseTime = 0,
                P95ResponseTime = 0,
                P99ResponseTime = 0,
                SuccessRate = 100.0,
                ErrorRate = 0,
                Throughput = 0
            });

        public virtual Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewaySuccessRateMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalAttempts = 0,
                SuccessfulDeliveries = 0,
                SuccessRate = 100.0,
                TrendDirection = 0
            });

        public virtual Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayFailureRateMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalFailures = 0,
                FailureRate = 0.0
            });

        public virtual Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayLatencyMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                AverageLatencyMs = 0,
                MedianLatencyMs = 0,
                P95LatencyMs = 0,
                P99LatencyMs = 0,
                MinLatencyMs = 0,
                MaxLatencyMs = 0
            });

        public virtual Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayDeliveryCountMetrics
            {
                StartDate = startDate,
                EndDate = endDate,
                TotalSent = 0,
                TotalDelivered = 0,
                TotalRead = 0,
                TotalBounced = 0,
                TotalComplained = 0,
                DeliveryRate = 0,
                ReadRate = 0,
                BounceRate = 0
            });

        public virtual Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayAnalyticsDashboard
            {
                StartDate = startDate,
                EndDate = endDate
            });

        public virtual Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
            => Task.FromResult(new GatewayRealTimeMetrics
            {
                Timestamp = DateTime.UtcNow,
                MessagesInLast5Minutes = 0,
                MessagesInLastHour = 0,
                MessagesInLastDay = 0,
                CurrentSuccessRate = 100.0,
                CurrentLatencyMs = 0,
                ActiveConnections = 0,
                QueuedMessages = 0,
                HealthStatus = "healthy"
            });

        public virtual Task LogMessageAsync(string messageId, string status, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default)
            => _dataService.LogMessageAsync(messageId, _gatewayName, ProviderName, "push", status,
                requestPayload: null, responseData: null, errorMessage: null, errorCode: null,
                latencyMs: null, recipient: null, subject: null, messageType: "push",
                retryCount: 0, additionalInfo: metadata, gatewayConfigurationId: null);

        public virtual Task RecordMetricAsync(string metricName, double value, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default)
            => _dataService.SaveMetricAsync(_gatewayName, ProviderName, metricName, "realtime",
                DateTime.UtcNow, DateTime.UtcNow, totalMessages: (long)value, successfulMessages: 0,
                failedMessages: 0, averageLatencyMs: 0, metricData: tags?.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value),
                gatewayConfigurationId: null);
    }
}
