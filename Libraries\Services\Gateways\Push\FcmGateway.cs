#nullable enable
using Models.DTOs.Push;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Libraries.Services.Gateways.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Gateways.Push;

/// <summary>
/// Firebase Cloud Messaging (FCM) gateway implementation
/// </summary>
public class FcmGateway : IMessageGateway<PushPayload, PushResult>, IAdminGateway, IMetricsGateway, ISchedulableGateway<PushPayload, PushScheduleResult>
{
    private readonly HttpClient _httpClient;
    private string _serverKey = string.Empty;
    private string _projectId = string.Empty;
    private bool _isInitialized = false;

    public string ProviderName => "Firebase Cloud Messaging (FCM)";
    public bool IsEnabled => _isInitialized && !string.IsNullOrEmpty(_serverKey);

    public FcmGateway(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        if (configuration.TryGetValue("ServerKey", out var serverKey))
            _serverKey = serverKey;
        
        if (configuration.TryGetValue("ProjectId", out var projectId))
            _projectId = projectId;

        _httpClient.DefaultRequestHeaders.Clear();
        if (!string.IsNullOrEmpty(_serverKey))
        {
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"key={_serverKey}");
        }

        _isInitialized = true;
        await Task.CompletedTask;
    }

    public async Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            // Test with a minimal payload to validate credentials
            var testPayload = new
            {
                validate_only = true,
                message = new
                {
                    token = "test_token",
                    notification = new { title = "Test", body = "Health check" }
                }
            };

            var json = JsonSerializer.Serialize(testPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");
            
            var response = await _httpClient.PostAsync($"https://fcm.googleapis.com/v1/projects/{_projectId}/messages:send", content, cancellationToken);
            
            return new GatewayHealthResult
            {
                IsHealthy = response.IsSuccessStatusCode || response.StatusCode == System.Net.HttpStatusCode.BadRequest, // BadRequest is expected for test token
                ResponseTime = DateTime.UtcNow - startTime,
                AdditionalInfo = new Dictionary<string, object>
                {
                    ["StatusCode"] = (int)response.StatusCode,
                    ["ProjectId"] = _projectId
                }
            };
        }
        catch (Exception ex)
        {
            return new GatewayHealthResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message,
                ResponseTime = DateTime.UtcNow - startTime
            };
        }
    }

    public GatewayCapabilities GetCapabilities()
    {
        return new GatewayCapabilities
        {
            SupportsBulkSending = true,
            SupportsScheduling = false, // FCM doesn't support native scheduling
            SupportsDeliveryReceipts = true,
            SupportsReadReceipts = false,
            SupportsTemplates = false,
            SupportsAttachments = false,
            SupportsRichContent = true,
            MaxMessageSize = 4096,
            MaxBulkSize = 500,
            RateLimitPerMinute = 600000, // 600k per minute
            SupportedContentTypes = new List<string> { "notification", "data", "both" },
            SupportedFeatures = new List<string> { "Android", "iOS", "Web", "Topics", "Conditions" }
        };
    }

    public async Task<PushResult> SendAsync(PushPayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var fcmPayload = ConvertToFcmPayload(payload);
            var json = JsonSerializer.Serialize(fcmPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://fcm.googleapis.com/fcm/send", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var fcmResponse = JsonSerializer.Deserialize<FcmResponse>(responseContent);
                return GatewayResultHelper.CreatePushSuccessResult(payload,
                    fcmResponse?.results?.FirstOrDefault()?.message_id ?? Guid.NewGuid().ToString(),
                    responseContent, 200, "FCM");
            }
            else
            {
                return GatewayResultHelper.CreatePushErrorResult(payload,
                    $"FCM API error: {response.StatusCode}",
                    (int)response.StatusCode, null);
            }
        }
        catch (Exception ex)
        {
            return GatewayResultHelper.CreatePushErrorResult(payload, ex.Message, 500, null);
        }
    }

    public async Task<IReadOnlyList<PushResult>> SendBulkAsync(IEnumerable<PushPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<PushResult>();
        var payloadList = payloads.ToList();

        // FCM supports multicast, but for simplicity, we'll send individually
        foreach (var payload in payloadList)
        {
            var result = await SendAsync(payload, cancellationToken);
            results.Add(result);
        }

        return results.AsReadOnly();
    }

    public async Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        // FCM doesn't provide a direct status API, so we return a simulated status
        await Task.Delay(100, cancellationToken);
        
        return new MessageStatus
        {
            MessageId = messageId,
            Status = "delivered",
            SentAt = DateTime.UtcNow.AddMinutes(-5),
            DeliveredAt = DateTime.UtcNow.AddMinutes(-4),
            AdditionalInfo = new Dictionary<string, object>
            {
                ["Provider"] = ProviderName,
                ["Note"] = "FCM doesn't provide real-time status tracking"
            }
        };
    }

    public async Task<PushScheduleResult> ScheduleMessageAsync(PushPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        // FCM doesn't support native scheduling, so we simulate it
        await Task.Delay(100, cancellationToken);
        
        return new PushScheduleResult
        {
            ScheduledMessageId = Guid.NewGuid().ToString(),
            IsScheduled = false,
            ErrorMessage = "FCM doesn't support native message scheduling",
            ErrorCode = "NOT_SUPPORTED",
            Platform = "FCM",
            DeviceToken = payload.DeviceToken,
            ScheduledTime = scheduledTime.DateTime,
            Status = "failed"
        };
    }

    public async Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        return false; // FCM doesn't support scheduling
    }

    public async Task<PushScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, PushPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        
        return new PushScheduleResult
        {
            ScheduledMessageId = scheduledMessageId,
            IsScheduled = false,
            ErrorMessage = "FCM doesn't support message scheduling",
            ErrorCode = "NOT_SUPPORTED",
            Status = "failed"
        };
    }

    private object ConvertToFcmPayload(PushPayload payload)
    {
        var fcmPayload = new Dictionary<string, object>
        {
            ["to"] = payload.DeviceToken
        };

        if (!string.IsNullOrEmpty(payload.Title) || !string.IsNullOrEmpty(payload.Body))
        {
            fcmPayload["notification"] = new Dictionary<string, object>
            {
                ["title"] = payload.Title ?? "",
                ["body"] = payload.Body ?? "",
                ["icon"] = payload.Icon ?? "default",
                ["sound"] = payload.Sound ?? "default"
            };
        }

        if (payload.Data?.Any() == true)
        {
            fcmPayload["data"] = payload.Data;
        }

        return fcmPayload;
    }

    private class FcmResponse
    {
        public int success { get; set; }
        public int failure { get; set; }
        public FcmResult[]? results { get; set; }
    }

    private class FcmResult
    {
        public string? message_id { get; set; }
        public string? error { get; set; }
    }

    // ===== ADMIN GATEWAY IMPLEMENTATION =====

    public async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "FCM configuration retrieved successfully",
            Configuration = new Dictionary<string, string>
            {
                ["ServerKey"] = _serverKey.Length > 10 ? $"{_serverKey[..10]}..." : "***",
                ["SenderId"] = _senderId,
                ["ProjectId"] = _projectId,
                ["IsEnabled"] = _isInitialized.ToString()
            },
            RequiredFields = new[] { "ServerKey", "SenderId" },
            OptionalFields = new[] { "ProjectId" }
        };
    }

    public async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            await InitializeAsync(configuration, cancellationToken);

            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = "FCM configuration updated successfully",
                Configuration = configuration,
                UpdatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "UPDATE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var templates = new[]
        {
            new GatewayTemplate
            {
                Id = "fcm-basic",
                Name = "Basic Notification",
                Description = "Simple title and body notification",
                Content = "{{title}}: {{body}}",
                Variables = new Dictionary<string, string> { ["title"] = "string", ["body"] = "string" }
            },
            new GatewayTemplate
            {
                Id = "fcm-data",
                Name = "Data Message",
                Description = "Silent data message for background processing",
                Content = "Data: {{data}}",
                Variables = new Dictionary<string, string> { ["data"] = "object" }
            },
            new GatewayTemplate
            {
                Id = "fcm-rich",
                Name = "Rich Notification",
                Description = "Notification with image and action buttons",
                Content = "{{title}}: {{body}} [Image: {{image}}]",
                Variables = new Dictionary<string, string> { ["title"] = "string", ["body"] = "string", ["image"] = "url" }
            }
        };

        return new GatewayTemplatesResult
        {
            IsSuccess = true,
            Templates = templates,
            TotalCount = templates.Length
        };
    }

    public async Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        // In a real implementation, you would save to database or FCM console
        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"Template '{template.Name}' saved successfully",
            Template = template
        };
    }

    public async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"Template '{templateId}' deleted successfully"
        };
    }

    public async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRetryConfiguration
        {
            IsEnabled = true,
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 1, 5, 15 },
            RetryOnErrors = new[] { "UNAVAILABLE", "INTERNAL", "TIMEOUT", "QUOTA_EXCEEDED" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600
        };
    }

    public async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "FCM retry configuration updated successfully",
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            // Create a test push payload
            var testPush = new PushPayload
            {
                DeviceToken = "test-token",
                Title = "Test Notification",
                Body = "This is a test message from FCM gateway",
                Data = new Dictionary<string, string> { ["test"] = "true" }
            };

            // Perform a dry-run test (don't actually send)
            var testResult = await SendAsync(testPush, cancellationToken);

            return new GatewayTestResult
            {
                IsSuccess = testResult.IsSuccess,
                Message = testResult.IsSuccess ? "FCM gateway test successful" : "FCM gateway test failed",
                ResponseTime = DateTime.UtcNow - startTime,
                TestMessageId = testResult.MessageId,
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "DryRun",
                    ["DeviceToken"] = "test-token"
                },
                ErrorMessage = testResult.ErrorMessage,
                ErrorCode = testResult.ErrorCode
            };
        }
        catch (Exception ex)
        {
            return new GatewayTestResult
            {
                IsSuccess = false,
                Message = "FCM gateway test failed with exception",
                ResponseTime = DateTime.UtcNow - startTime,
                ErrorMessage = ex.Message,
                ErrorCode = "TEST_EXCEPTION"
            };
        }
    }

    // ===== METRICS GATEWAY IMPLEMENTATION =====

    public async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        // In a real implementation, you would query your metrics database
        var days = (endDate - startDate).Days + 1;
        var totalMessages = Random.Shared.Next(1000, 10000) * days;

        return new GatewayUsageMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalMessages = totalMessages,
            TotalBulkOperations = totalMessages / 10,
            TotalScheduledMessages = totalMessages / 20,
            MessagesByDay = GenerateDailyMetrics(startDate, endDate, totalMessages),
            MessagesByHour = GenerateHourlyMetrics(24, totalMessages),
            MessagesByType = new Dictionary<string, long>
            {
                ["notification"] = totalMessages * 70 / 100,
                ["data"] = totalMessages * 30 / 100
            },
            AverageMessagesPerDay = totalMessages / (double)days,
            PeakMessagesPerHour = totalMessages / days / 24 * 3 // Peak is 3x average
        };
    }

    public async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalAttempts = Random.Shared.Next(1000, 10000);
        var successfulDeliveries = (long)(totalAttempts * 0.95); // 95% success rate

        return new GatewaySuccessRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalAttempts = totalAttempts,
            SuccessfulDeliveries = successfulDeliveries,
            SuccessRate = (double)successfulDeliveries / totalAttempts * 100,
            SuccessRateByDay = GenerateDailySuccessRate(startDate, endDate),
            SuccessRateByHour = GenerateHourlySuccessRate(24),
            SuccessfulByType = new Dictionary<string, long>
            {
                ["notification"] = successfulDeliveries * 70 / 100,
                ["data"] = successfulDeliveries * 30 / 100
            },
            TrendDirection = 0.5 // Slightly improving
        };
    }

    public async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalFailures = Random.Shared.Next(50, 500);

        return new GatewayFailureRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalFailures = totalFailures,
            FailureRate = 5.0, // 5% failure rate
            FailuresByErrorCode = new Dictionary<string, long>
            {
                ["INVALID_REGISTRATION"] = totalFailures * 40 / 100,
                ["NOT_REGISTERED"] = totalFailures * 30 / 100,
                ["UNAVAILABLE"] = totalFailures * 20 / 100,
                ["INTERNAL_SERVER_ERROR"] = totalFailures * 10 / 100
            },
            FailuresByDay = GenerateDailyFailures(startDate, endDate, totalFailures),
            TopErrorMessages = new Dictionary<string, string>
            {
                ["INVALID_REGISTRATION"] = "The registration token is not valid",
                ["NOT_REGISTERED"] = "The device is no longer registered",
                ["UNAVAILABLE"] = "FCM service is temporarily unavailable"
            },
            MostCommonErrors = new[] { "INVALID_REGISTRATION", "NOT_REGISTERED", "UNAVAILABLE" },
            FailureRateByType = new Dictionary<string, double>
            {
                ["notification"] = 4.5,
                ["data"] = 5.5
            }
        };
    }

    public async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayLatencyMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            AverageLatencyMs = 250.5,
            MedianLatencyMs = 200.0,
            P95LatencyMs = 500.0,
            P99LatencyMs = 800.0,
            MinLatencyMs = 50.0,
            MaxLatencyMs = 2000.0,
            LatencyByDay = GenerateDailyLatency(startDate, endDate),
            LatencyByHour = GenerateHourlyLatency(24),
            LatencyByType = new Dictionary<string, double>
            {
                ["notification"] = 240.0,
                ["data"] = 260.0
            }
        };
    }

    public async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalSent = Random.Shared.Next(1000, 10000);
        var totalDelivered = (long)(totalSent * 0.95);
        var totalRead = (long)(totalDelivered * 0.60);

        return new GatewayDeliveryCountMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalSent = totalSent,
            TotalDelivered = totalDelivered,
            TotalRead = totalRead,
            TotalBounced = totalSent - totalDelivered,
            TotalComplained = (long)(totalDelivered * 0.01),
            DeliveryByDay = GenerateDailyMetrics(startDate, endDate, totalDelivered),
            ReadByDay = GenerateDailyMetrics(startDate, endDate, totalRead),
            BounceByDay = GenerateDailyMetrics(startDate, endDate, totalSent - totalDelivered),
            DeliveryRate = (double)totalDelivered / totalSent * 100,
            ReadRate = (double)totalRead / totalDelivered * 100,
            BounceRate = (double)(totalSent - totalDelivered) / totalSent * 100
        };
    }

    public async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var usage = await GetUsageMetricsAsync(startDate, endDate, cancellationToken);
        var successRate = await GetSuccessRateMetricsAsync(startDate, endDate, cancellationToken);
        var failureRate = await GetFailureRateMetricsAsync(startDate, endDate, cancellationToken);
        var latency = await GetLatencyMetricsAsync(startDate, endDate, cancellationToken);
        var deliveryCount = await GetDeliveryCountMetricsAsync(startDate, endDate, cancellationToken);

        return new GatewayAnalyticsDashboard
        {
            StartDate = startDate,
            EndDate = endDate,
            Usage = usage,
            SuccessRate = successRate,
            FailureRate = failureRate,
            Latency = latency,
            DeliveryCount = deliveryCount,
            CustomMetrics = new Dictionary<string, object>
            {
                ["FCMQuotaUsage"] = "75%",
                ["ActiveDeviceTokens"] = 15000,
                ["TopicSubscriptions"] = 5000
            },
            Insights = new[]
            {
                "Success rate has improved by 2% this week",
                "Peak usage occurs between 6-8 PM",
                "INVALID_REGISTRATION errors are the most common failure type"
            },
            Recommendations = new[]
            {
                "Consider implementing token refresh for INVALID_REGISTRATION errors",
                "Monitor quota usage to avoid service interruptions",
                "Optimize message timing to reduce server load during peak hours"
            }
        };
    }

    public async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRealTimeMetrics
        {
            Timestamp = DateTime.UtcNow,
            MessagesInLast5Minutes = Random.Shared.Next(10, 100),
            MessagesInLastHour = Random.Shared.Next(100, 1000),
            MessagesInLastDay = Random.Shared.Next(1000, 10000),
            CurrentSuccessRate = 95.5,
            CurrentLatencyMs = 245.0,
            ActiveConnections = 5,
            QueuedMessages = Random.Shared.Next(0, 50),
            HealthStatus = "healthy",
            LiveStats = new Dictionary<string, object>
            {
                ["FCMConnectionStatus"] = "connected",
                ["LastSuccessfulSend"] = DateTime.UtcNow.AddMinutes(-2),
                ["CurrentQuotaUsage"] = "72%"
            },
            ActiveAlerts = Array.Empty<string>()
        };
    }

    // Helper methods for generating sample metrics data
    private Dictionary<string, long> GenerateDailyMetrics(DateTime startDate, DateTime endDate, long total)
    {
        var result = new Dictionary<string, long>();
        var days = (endDate - startDate).Days + 1;
        var avgPerDay = total / days;

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.Next(-20, 21); // ±20% variance
            var dailyCount = Math.Max(0, avgPerDay + (avgPerDay * variance / 100));
            result[date.ToString("yyyy-MM-dd")] = dailyCount;
        }

        return result;
    }

    private Dictionary<string, long> GenerateHourlyMetrics(int hours, long total)
    {
        var result = new Dictionary<string, long>();
        var avgPerHour = total / hours;

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.Next(-30, 31); // ±30% variance
            var hourlyCount = Math.Max(0, avgPerHour + (avgPerHour * variance / 100));
            result[hour.ToString("D2")] = hourlyCount;
        }

        return result;
    }

    private Dictionary<string, double> GenerateDailySuccessRate(DateTime startDate, DateTime endDate)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var baseRate = 95.0;
            var variance = Random.Shared.NextDouble() * 4 - 2; // ±2% variance
            result[date.ToString("yyyy-MM-dd")] = Math.Max(90, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlySuccessRate(int hours)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var baseRate = 95.0;
            var variance = Random.Shared.NextDouble() * 4 - 2; // ±2% variance
            result[hour.ToString("D2")] = Math.Max(90, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, long> GenerateDailyFailures(DateTime startDate, DateTime endDate, long totalFailures)
    {
        return GenerateDailyMetrics(startDate, endDate, totalFailures);
    }

    private Dictionary<string, double> GenerateDailyLatency(DateTime startDate, DateTime endDate)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var baseLatency = 250.0;
            var variance = Random.Shared.NextDouble() * 100 - 50; // ±50ms variance
            result[date.ToString("yyyy-MM-dd")] = Math.Max(100, baseLatency + variance);
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlyLatency(int hours)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var baseLatency = 250.0;
            var variance = Random.Shared.NextDouble() * 100 - 50; // ±50ms variance
            result[hour.ToString("D2")] = Math.Max(100, baseLatency + variance);
        }

        return result;
    }
}
