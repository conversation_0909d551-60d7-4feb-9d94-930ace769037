using Core.Interfaces;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using Models.DTOs.SMS;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// SMS messaging endpoints for sending text messages
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class SmsController : ControllerBase, IMessageSender<SmsPayload, SmsResult>
{
    private readonly IMessageSender<SmsPayload, SmsResult> _smsSender;

    public SmsController(IMessageSender<SmsPayload, SmsResult> smsSender)
    {
        _smsSender = smsSender;
    }

    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="payload">SMS message payload with recipient and content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>SMS send result</returns>
    /// <response code="200">SMS message sent successfully</response>
    /// <response code="400">Invalid payload or send failed</response>
    [HttpPost("send-single")]
    [ProducesResponseType(typeof(SmsResult), 200)]
    [ProducesResponseType(400)]
    public Task<SmsResult> SendAsync([FromBody] SmsPayload payload, CancellationToken cancellationToken = default)
    {
        return _smsSender.SendAsync(payload, cancellationToken);
    }

    /// <summary>
    /// Send SMS messages in bulk
    /// </summary>
    /// <param name="payloads">Collection of SMS payloads to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of SMS send results</returns>
    /// <response code="200">SMS messages sent successfully</response>
    /// <response code="400">Invalid payloads or send failed</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(IReadOnlyList<SmsResult>), 200)]
    [ProducesResponseType(400)]
    public Task<IReadOnlyList<SmsResult>> SendBulkAsync(IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results =  _smsSender.SendBulkAsync(payloads, cancellationToken);
        return results;
    }
}
