#nullable enable
using System;

namespace Models.DTOs.Email;

/// <summary>
/// Result of email sending operation
/// </summary>
public class EmailResult
{
    /// <summary>
    /// Unique message identifier from SendGrid
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the email was sent successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Timestamp when the email was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Recipient email address
    /// </summary>
    public string Recipient { get; set; } = string.Empty;

    /// <summary>
    /// Email subject
    /// </summary>
    public string Subject { get; set; } = string.Empty;

    /// <summary>
    /// SendGrid status code
    /// </summary>
    public int? StatusCode { get; set; }

    /// <summary>
    /// Additional metadata from SendGrid
    /// </summary>
    public object? Metadata { get; set; }
}

/// <summary>
/// Email schedule result
/// </summary>
public class EmailScheduleResult
{
    /// <summary>
    /// Unique scheduled message identifier
    /// </summary>
    public string ScheduledMessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if scheduling failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Target email address
    /// </summary>
    public string? ToEmail { get; set; }

    /// <summary>
    /// Email subject
    /// </summary>
    public string? Subject { get; set; }

    /// <summary>
    /// Schedule status (scheduled, cancelled, sent, failed)
    /// </summary>
    public string Status { get; set; } = "scheduled";

    /// <summary>
    /// Provider name
    /// </summary>
    public string? Provider { get; set; }

    /// <summary>
    /// Whether the scheduled message can be cancelled
    /// </summary>
    public bool CanCancel { get; set; } = true;

    /// <summary>
    /// Whether the scheduled message can be modified
    /// </summary>
    public bool CanModify { get; set; } = false;

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Additional scheduling metadata
    /// </summary>
    public object? SchedulingMetadata { get; set; }
}
