using Data.Mapping;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.DbEntities.Gateway;

namespace Data.Mapping.Gateway;

public class GatewayTemplateMapping : MappingEntityTypeConfiguration<GatewayTemplate>
{
    public override void Configure(EntityTypeBuilder<GatewayTemplate> builder)
    {
        builder.ToTable("GatewayTemplates");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.TemplateId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.Name)
            .IsRequired()
            .HasMaxLength(200);

        builder.Property(x => x.Description)
            .HasMaxLength(500);

        builder.Property(x => x.Content)
            .IsRequired();

        builder.Property(x => x.ContentType)
            .HasMaxLength(50)
            .HasDefaultValue("text/plain");

        builder.Property(x => x.Variables)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.Tags)
            .HasColumnType("jsonb")
            .HasDefaultValue("[]");

        builder.Property(x => x.IsActive)
            .HasDefaultValue(true);

        builder.Property(x => x.CreatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.UpdatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.CreatedBy)
            .HasMaxLength(100);

        builder.Property(x => x.UpdatedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(x => new { x.GatewayConfigurationId, x.TemplateId })
            .IsUnique()
            .HasDatabaseName("IX_GatewayTemplates_GatewayConfigurationId_TemplateId");

        builder.HasIndex(x => x.IsActive)
            .HasDatabaseName("IX_GatewayTemplates_IsActive");

        builder.HasIndex(x => x.ContentType)
            .HasDatabaseName("IX_GatewayTemplates_ContentType");
    }
}
