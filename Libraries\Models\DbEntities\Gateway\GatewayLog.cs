using Models.DbEntities;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models.DbEntities.Gateway;

[Table("GatewayLogs")]
public class GatewayLog : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string MessageId { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string GatewayName { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string Provider { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string Operation { get; set; } = string.Empty; // Send, SendBulk, GetStatus, etc.

    [Required]
    [MaxLength(50)]
    public string Status { get; set; } = string.Empty; // Success, Failed, Pending

    [Column(TypeName = "jsonb")]
    public string? RequestPayload { get; set; } // JSON request data

    [Column(TypeName = "jsonb")]
    public string? ResponseData { get; set; } // JSON response data

    [MaxLength(1000)]
    public string? ErrorMessage { get; set; }

    [MaxLength(100)]
    public string? ErrorCode { get; set; }

    public double? LatencyMs { get; set; }

    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    [MaxLength(200)]
    public string? Recipient { get; set; }

    [MaxLength(500)]
    public string? Subject { get; set; }

    [MaxLength(50)]
    public string? MessageType { get; set; } // text, html, image, etc.

    public int? RetryCount { get; set; } = 0;

    [Column(TypeName = "jsonb")]
    public string? AdditionalInfo { get; set; } // JSON additional data

    // Foreign key
    public int GatewayConfigurationId { get; set; }

    // Navigation property
    [ForeignKey("GatewayConfigurationId")]
    public virtual GatewayConfiguration GatewayConfiguration { get; set; } = null!;
}
