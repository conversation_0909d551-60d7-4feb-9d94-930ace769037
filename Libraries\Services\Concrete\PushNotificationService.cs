using System.Net.Http;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.Push;

namespace Services.Concrete;

public class PushNotificationService
{
    private readonly HttpClient _httpClient;

    public PushNotificationService(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task<PushNotificationResult> SendNotificationAsync(PushNotificationPayload payload, CancellationToken cancellationToken)
    {
        var requestContent = JsonSerializer.Serialize(payload);
        var response = await _httpClient.PostAsync("https://api.rapidapi.com/push/send", new StringContent(requestContent), cancellationToken);

        if (!response.IsSuccessStatusCode)
        {
            return new PushNotificationResult
            {
                IsSuccess = false,
                ErrorMessage = await response.Content.ReadAsStringAsync()
            };
        }

        var result = JsonSerializer.Deserialize<PushNotificationResult>(await response.Content.ReadAsStringAsync());
        return result ?? new PushNotificationResult { IsSuccess = false, ErrorMessage = "Unknown error" };
    }
}