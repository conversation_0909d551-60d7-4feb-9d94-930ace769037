using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Email;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Email messaging endpoints for sending emails via SendGrid
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class EmailController : ControllerBase
{
    private readonly IMessageSender<EmailPayload, EmailResult> _emailSender;

    public EmailController(IMessageSender<EmailPayload, EmailResult> emailSender)
    {
        _emailSender = emailSender;
    }

    /// <summary>
    /// Send a single email via SendGrid
    /// </summary>
    /// <param name="payload">Email payload with recipient, subject, and content</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email send result with message ID and status</returns>
    /// <response code="200">Email sent successfully</response>
    /// <response code="400">Invalid payload or send failed</response>
    [HttpPost("send-single")]
    [ProducesResponseType(typeof(EmailResult), 200)]
    [ProducesResponseType(400)]
    public async Task<EmailResult> SendAsync([FromBody] EmailPayload payload, CancellationToken cancellationToken = default)
    {
        return await _emailSender.SendAsync(payload, cancellationToken);
    }

    /// <summary>
    /// Send multiple emails in bulk via SendGrid
    /// </summary>
    /// <param name="payloads">List of email payloads to send</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>List of email send results</returns>
    /// <response code="200">Bulk emails processed (check individual results for status)</response>
    /// <response code="400">Invalid payload</response>
    [HttpPost("send-bulk")]
    [ProducesResponseType(typeof(IReadOnlyList<EmailResult>), 200)]
    [ProducesResponseType(400)]
    public async Task<IReadOnlyList<EmailResult>> SendBulkAsync([FromBody] IEnumerable<EmailPayload> payloads, CancellationToken cancellationToken = default)
    {
        return await _emailSender.SendBulkAsync(payloads, cancellationToken);
    }

    /// <summary>
    /// Resend an email using the original message ID
    /// </summary>
    /// <param name="messageId">Original message ID to resend</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Resend result</returns>
    /// <response code="200">Email resent successfully</response>
    /// <response code="400">Invalid message ID or resend failed</response>
    [HttpPost("resend/{messageId}")]
    [ProducesResponseType(typeof(EmailResult), 200)]
    [ProducesResponseType(400)]
    public async Task<EmailResult> ResendAsync(string messageId, CancellationToken cancellationToken = default)
    {
        return await _emailSender.ResendAsync(messageId, cancellationToken);
    }

    /// <summary>
    /// Get the status of a sent email
    /// </summary>
    /// <param name="messageId">Message ID to check status for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email status information</returns>
    /// <response code="200">Status retrieved successfully</response>
    /// <response code="404">Message not found</response>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    public async Task<object> GetStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        return await _emailSender.GetStatusAsync(messageId, cancellationToken);
    }

    /// <summary>
    /// Get delivery receipt for a sent email
    /// </summary>
    /// <param name="messageId">Message ID to get delivery receipt for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Email delivery receipt information</returns>
    /// <response code="200">Delivery receipt retrieved successfully</response>
    /// <response code="404">Message not found</response>
    [HttpGet("delivery-receipt/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    public async Task<object> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default)
    {
        return await _emailSender.GetDeliveryReceiptAsync(messageId, cancellationToken);
    }

    /// <summary>
    /// Send raw SendGrid payload for advanced use cases
    /// </summary>
    /// <param name="rawPayload">Raw SendGrid message object</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Raw send result</returns>
    /// <response code="200">Raw payload sent successfully</response>
    /// <response code="400">Invalid raw payload</response>
    [HttpPost("send-raw")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<object> SendRawPayloadAsync([FromBody] object rawPayload, CancellationToken cancellationToken = default)
    {
        return await _emailSender.SendRawPayloadAsync(rawPayload, cancellationToken);
    }

    /// <summary>
    /// Prepare and validate an email payload before sending
    /// </summary>
    /// <param name="payload">Email payload to prepare</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Prepared and validated email payload</returns>
    /// <response code="200">Payload prepared successfully</response>
    /// <response code="400">Invalid payload</response>
    [HttpPost("prepare")]
    [ProducesResponseType(typeof(EmailPayload), 200)]
    [ProducesResponseType(400)]
    public async Task<EmailPayload> PrepareMessageAsync([FromBody] EmailPayload payload, CancellationToken cancellationToken = default)
    {
        return await _emailSender.PrepareMessageAsync(payload, cancellationToken);
    }
}
