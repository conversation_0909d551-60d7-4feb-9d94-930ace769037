using System;
using System.Collections.Generic;
using Models.DTOs.Email;
using Models.DTOs.SMS;
using Models.DTOs.MessengerApp;
using Models.DTOs.Push;

namespace Libraries.Services.Gateways.Utils
{
    /// <summary>
    /// Utility class for creating standardized gateway results with correct property mappings
    /// </summary>
    public static class GatewayResultHelper
    {
        // Email Result Helpers
        public static EmailResult CreateEmailSuccessResult(EmailPayload payload, string messageId, string responseContent = null, int statusCode = 200, string gatewayName = null)
        {
            return new EmailResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                Recipient = payload.To,
                Subject = payload.Subject,
                SentAt = DateTime.UtcNow,
                StatusCode = statusCode,
                Metadata = responseContent != null ? new Dictionary<string, object> { [$"{gatewayName?.ToLower() ?? "gateway"}_response"] = responseContent } : null
            };
        }

        public static EmailResult CreateEmailErrorResult(EmailPayload payload, string errorMessage, int statusCode = 500, string messageId = null)
        {
            return new EmailResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                Recipient = payload.To,
                Subject = payload.Subject,
                SentAt = DateTime.UtcNow,
                StatusCode = statusCode
            };
        }

        public static EmailScheduleResult CreateEmailSuccessScheduleResult(EmailPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null, int statusCode = 200, string gatewayName = null)
        {
            return new EmailScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                Recipient = payload.To,
                Subject = payload.Subject,
                ScheduledTime = scheduledTime.DateTime,
                StatusCode = statusCode,
                Metadata = responseContent != null ? new Dictionary<string, object> { [$"{gatewayName?.ToLower() ?? "gateway"}_response"] = responseContent } : null
            };
        }

        public static EmailScheduleResult CreateEmailErrorScheduleResult(EmailPayload payload, DateTimeOffset scheduledTime, string errorMessage, int statusCode = 500, string scheduledMessageId = null)
        {
            return new EmailScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                Recipient = payload.To,
                Subject = payload.Subject,
                ScheduledTime = scheduledTime.DateTime,
                StatusCode = statusCode
            };
        }

        // SMS Result Helpers
        public static SmsResult CreateSmsSuccessResult(SmsPayload payload, string messageId, string responseContent = null, string gatewayName = null)
        {
            return new SmsResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        public static SmsResult CreateSmsErrorResult(SmsPayload payload, string errorMessage, string messageId = null)
        {
            return new SmsResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        public static SmsScheduleResult CreateSmsSuccessScheduleResult(SmsPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null, string gatewayName = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        public static SmsScheduleResult CreateSmsErrorScheduleResult(SmsPayload payload, DateTimeOffset scheduledTime, string errorMessage, string scheduledMessageId = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // MessengerApp Result Helpers
        public static MessengerAppResult CreateMessengerAppSuccessResult(MessengerAppPayload payload, string messageId, string responseContent = null, string gatewayName = null)
        {
            return new MessengerAppResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        public static MessengerAppResult CreateMessengerAppErrorResult(MessengerAppPayload payload, string errorMessage, string messageId = null)
        {
            return new MessengerAppResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        public static MessengerAppScheduleResult CreateMessengerAppSuccessScheduleResult(MessengerAppPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null, string gatewayName = null)
        {
            return new MessengerAppScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        public static MessengerAppScheduleResult CreateMessengerAppErrorScheduleResult(MessengerAppPayload payload, DateTimeOffset scheduledTime, string errorMessage, string scheduledMessageId = null)
        {
            return new MessengerAppScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // Push Result Helpers
        public static PushResult CreatePushSuccessResult(PushPayload payload, string messageId, string responseContent = null, string gatewayName = null)
        {
            return new PushResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        public static PushResult CreatePushErrorResult(PushPayload payload, string errorMessage, string messageId = null)
        {
            return new PushResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        public static PushScheduleResult CreatePushSuccessScheduleResult(PushPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null, string gatewayName = null)
        {
            return new PushScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        public static PushScheduleResult CreatePushErrorScheduleResult(PushPayload payload, DateTimeOffset scheduledTime, string errorMessage, string scheduledMessageId = null)
        {
            return new PushScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // Test Payload Helpers
        public static EmailPayload CreateTestEmailPayload(string gatewayName)
        {
            return new EmailPayload
            {
                To = "<EMAIL>",
                From = "<EMAIL>",
                Subject = $"Test email from {gatewayName} gateway",
                Body = $"This is a test message from {gatewayName} gateway",
                IsHtml = false
            };
        }

        public static SmsPayload CreateTestSmsPayload(string gatewayName)
        {
            return new SmsPayload
            {
                Sender = "TEST",
                Recipients = new List<string> { "+1234567890" },
                Message = $"Test message from {gatewayName} gateway"
            };
        }

        public static MessengerAppPayload CreateTestMessengerAppPayload(string gatewayName)
        {
            return new MessengerAppPayload
            {
                Platform = gatewayName,
                RecipientId = "test_recipient",
                MessageType = "text",
                Text = $"Test message from {gatewayName} gateway"
            };
        }

        public static PushPayload CreateTestPushPayload(string gatewayName)
        {
            return new PushPayload
            {
                DeviceTokens = new List<string> { "test_device_token" },
                Title = $"Test from {gatewayName}",
                Body = $"Test push notification from {gatewayName} gateway"
            };
        }
    }
}
