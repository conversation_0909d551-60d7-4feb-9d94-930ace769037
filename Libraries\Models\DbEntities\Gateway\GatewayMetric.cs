using Models.DbEntities;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models.DbEntities.Gateway;

[Table("GatewayMetrics")]
public class GatewayMetric : BaseEntity
{
    [Required]
    [MaxLength(50)]
    public string GatewayName { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string Provider { get; set; } = string.Empty;

    [Required]
    [MaxLength(50)]
    public string MetricType { get; set; } = string.Empty; // Usage, SuccessRate, FailureRate, Latency, DeliveryCount

    [Required]
    [MaxLength(50)]
    public string Period { get; set; } = string.Empty; // Hourly, Daily, Weekly, Monthly

    public DateTime PeriodStart { get; set; }

    public DateTime PeriodEnd { get; set; }

    public long TotalMessages { get; set; } = 0;

    public long SuccessfulMessages { get; set; } = 0;

    public long FailedMessages { get; set; } = 0;

    public double SuccessRate { get; set; } = 0.0;

    public double FailureRate { get; set; } = 0.0;

    public double AverageLatencyMs { get; set; } = 0.0;

    public double MinLatencyMs { get; set; } = 0.0;

    public double MaxLatencyMs { get; set; } = 0.0;

    public long DeliveredMessages { get; set; } = 0;

    public long ReadMessages { get; set; } = 0;

    public long BouncedMessages { get; set; } = 0;

    public long ComplainedMessages { get; set; } = 0;

    [Column(TypeName = "jsonb")]
    public string? MetricData { get; set; } = "{}"; // JSON detailed metrics

    [Column(TypeName = "jsonb")]
    public string? MessagesByType { get; set; } = "{}"; // JSON breakdown by message type

    [Column(TypeName = "jsonb")]
    public string? ErrorBreakdown { get; set; } = "{}"; // JSON error analysis

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    // Foreign key
    public int GatewayConfigurationId { get; set; }

    // Navigation property
    [ForeignKey("GatewayConfigurationId")]
    public virtual GatewayConfiguration GatewayConfiguration { get; set; } = null!;
}
