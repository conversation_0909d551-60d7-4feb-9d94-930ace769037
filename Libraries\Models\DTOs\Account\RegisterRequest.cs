﻿using System.ComponentModel.DataAnnotations;

namespace Models.DTOs.Account;

public class RegisterRequest
{
    [Required]
    [MinLength(6)]
    public string UserName { get; set; }

    [Required]
    public string FirstName { get; set; }

    [Required]
    public string LastName { get; set; }

    [Required]
    [EmailAddress]
    public string Email { get; set; }

    [Required]
    [MinLength(6)]
    public string Password { get; set; }

    [Required]
    [Compare("Password")]
    public string ConfirmPassword { get; set; }
}