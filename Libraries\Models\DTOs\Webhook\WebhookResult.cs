#nullable enable
using System;
using System.Collections.Generic;

namespace Models.DTOs.Webhook;

/// <summary>
/// Webhook send result
/// </summary>
public class WebhookResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the webhook was sent successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// HTTP status code received
    /// </summary>
    public int? StatusCode { get; set; }

    /// <summary>
    /// Response body from the webhook endpoint
    /// </summary>
    public string? ResponseBody { get; set; }

    /// <summary>
    /// Response headers from the webhook endpoint
    /// </summary>
    public Dictionary<string, string>? ResponseHeaders { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error type/category
    /// </summary>
    public string? ErrorType { get; set; }

    /// <summary>
    /// Target URL that was called
    /// </summary>
    public string? TargetUrl { get; set; }

    /// <summary>
    /// HTTP method used
    /// </summary>
    public string? Method { get; set; }

    /// <summary>
    /// Timestamp when the webhook was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Response time in milliseconds
    /// </summary>
    public long? ResponseTimeMs { get; set; }

    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Whether the maximum retry limit was reached
    /// </summary>
    public bool MaxRetriesReached { get; set; }

    /// <summary>
    /// Event type that triggered the webhook
    /// </summary>
    public string? EventType { get; set; }

    /// <summary>
    /// Event ID that triggered the webhook
    /// </summary>
    public string? EventId { get; set; }

    /// <summary>
    /// Additional metadata about the request
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Whether the response was redirected
    /// </summary>
    public bool WasRedirected { get; set; }

    /// <summary>
    /// Final URL after redirects
    /// </summary>
    public string? FinalUrl { get; set; }

    /// <summary>
    /// SSL certificate validation result
    /// </summary>
    public bool? SslValid { get; set; }

    /// <summary>
    /// Content type of the response
    /// </summary>
    public string? ResponseContentType { get; set; }

    /// <summary>
    /// Size of the response in bytes
    /// </summary>
    public long? ResponseSizeBytes { get; set; }
}

/// <summary>
/// Webhook schedule result
/// </summary>
public class WebhookScheduleResult
{
    /// <summary>
    /// Unique scheduled message identifier
    /// </summary>
    public string ScheduledMessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Target URL that will be called
    /// </summary>
    public string? TargetUrl { get; set; }

    /// <summary>
    /// HTTP method that will be used
    /// </summary>
    public string? Method { get; set; }

    /// <summary>
    /// Event type for the scheduled webhook
    /// </summary>
    public string? EventType { get; set; }

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Priority level of the scheduled webhook
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Whether the scheduled webhook can be cancelled
    /// </summary>
    public bool CanCancel { get; set; } = true;
}
