#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Webhook;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Webhook sending endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class WebhookSendController : ControllerBase
{
    private readonly IMessageSender<WebhookPayload, WebhookResult> _webhookSender;
    private readonly IMessageScheduler<WebhookPayload, WebhookScheduleResult> _webhookScheduler;

    public WebhookSendController(
        IMessageSender<WebhookPayload, WebhookResult> webhookSender,
        IMessageScheduler<WebhookPayload, WebhookScheduleResult> webhookScheduler)
    {
        _webhookSender = webhookSender;
        _webhookScheduler = webhookScheduler;
    }

    /// <summary>
    /// Send a single webhook
    /// </summary>
    /// <param name="payload">Webhook payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Send result</returns>
    /// <response code="200">Webhook sent successfully</response>
    /// <response code="400">Invalid payload</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(WebhookResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<WebhookResult>> Send([FromBody] WebhookPayload payload, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _webhookSender.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Send multiple webhooks in bulk
    /// </summary>
    /// <param name="payloads">List of webhook payloads</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk send results</returns>
    /// <response code="200">Webhooks sent successfully</response>
    /// <response code="400">Invalid payloads</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("sendbulk")]
    [ProducesResponseType(typeof(IReadOnlyList<WebhookResult>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<IReadOnlyList<WebhookResult>>> SendBulk([FromBody] IEnumerable<WebhookPayload> payloads, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var results = await _webhookSender.SendBulkAsync(payloads, cancellationToken);
        return Ok(results);
    }

    /// <summary>
    /// Schedule a webhook for future delivery
    /// </summary>
    /// <param name="request">Schedule request containing payload and delivery time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Schedule result</returns>
    /// <response code="200">Webhook scheduled successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("schedulesend")]
    [ProducesResponseType(typeof(WebhookScheduleResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<WebhookScheduleResult>> ScheduleSend([FromBody] ScheduleWebhookRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _webhookScheduler.ScheduleMessageAsync(request.Payload, request.ScheduledTime, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get status of a sent webhook
    /// </summary>
    /// <param name="messageId">Message ID to check status for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message status</returns>
    /// <response code="200">Status retrieved successfully</response>
    /// <response code="404">Message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<object>> GetStatus(string messageId, CancellationToken cancellationToken)
    {
        var status = await _webhookSender.GetStatusAsync(messageId, cancellationToken);
        return Ok(status);
    }

    /// <summary>
    /// Resend a previously sent webhook
    /// </summary>
    /// <param name="messageId">Original message ID to resend</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Resend result</returns>
    /// <response code="200">Webhook resent successfully</response>
    /// <response code="404">Original message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("resend/{messageId}")]
    [ProducesResponseType(typeof(WebhookResult), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<WebhookResult>> Resend(string messageId, CancellationToken cancellationToken)
    {
        var result = await _webhookSender.ResendAsync(messageId, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Test webhook endpoint connectivity
    /// </summary>
    /// <param name="request">Test request with endpoint URL</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Test result</returns>
    /// <response code="200">Test completed</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("test")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<object>> TestWebhook([FromBody] TestWebhookRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        return await Task.FromResult(Ok(new
        {
            Success = true,
            Url = request.Url,
            ResponseTime = 245,
            StatusCode = 200,
            Response = "OK",
            TestedAt = DateTime.UtcNow
        }));
    }
}

/// <summary>
/// Request model for scheduling webhooks
/// </summary>
public class ScheduleWebhookRequest
{
    /// <summary>
    /// Webhook payload
    /// </summary>
    public WebhookPayload Payload { get; set; } = new();

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTimeOffset ScheduledTime { get; set; }
}

/// <summary>
/// Request model for testing webhooks
/// </summary>
public class TestWebhookRequest
{
    /// <summary>
    /// Webhook URL to test
    /// </summary>
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// HTTP method to use
    /// </summary>
    public string Method { get; set; } = "POST";

    /// <summary>
    /// Test payload
    /// </summary>
    public object? Payload { get; set; }
}
