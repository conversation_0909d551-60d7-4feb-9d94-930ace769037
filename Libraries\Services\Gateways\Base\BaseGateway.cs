using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.Email;
using Models.DTOs.SMS;
using Models.DTOs.Push;
using Models.DTOs.MessengerApp;
using Models.DTOs.Gateway;
using Services.Gateway;
using Services.Interfaces;

namespace Services.Gateways.Base
{
    /// <summary>
    /// Base gateway class providing common functionality and standardized DTO property access
    /// </summary>
    public abstract class BaseGateway
    {
        protected readonly HttpClient _httpClient;
        protected readonly IGatewayDataService _dataService;
        protected readonly string _gatewayName;

        protected BaseGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _gatewayName = gatewayName ?? throw new ArgumentNullException(nameof(gatewayName));
        }

        #region SMS Payload Helper Methods
        
        /// <summary>
        /// Gets the primary recipient from SmsPayload.Recipients
        /// </summary>
        protected string GetSmsRecipient(SmsPayload payload)
        {
            return payload?.Recipients?.FirstOrDefault() ?? string.Empty;
        }

        /// <summary>
        /// Gets the sender from SmsPayload.Sender
        /// </summary>
        protected string GetSmsSender(SmsPayload payload)
        {
            return payload?.Sender ?? string.Empty;
        }

        /// <summary>
        /// Creates a test SMS payload with correct property structure
        /// </summary>
        protected SmsPayload CreateTestSmsPayload(string gatewayName)
        {
            return new SmsPayload
            {
                Sender = "TEST",
                Recipients = new List<string> { "+1234567890" },
                Message = $"Test message from {gatewayName} gateway",
                Metadata = new Dictionary<string, string>()
            };
        }

        #endregion

        #region Email Payload Helper Methods

        /// <summary>
        /// Gets the recipient from EmailPayload.To
        /// </summary>
        protected string GetEmailRecipient(EmailPayload payload)
        {
            return payload?.To ?? string.Empty;
        }

        /// <summary>
        /// Gets the sender from EmailPayload.From
        /// </summary>
        protected string GetEmailSender(EmailPayload payload)
        {
            return payload?.From ?? string.Empty;
        }

        /// <summary>
        /// Gets the email body content from EmailPayload.Body
        /// </summary>
        protected string GetEmailContent(EmailPayload payload)
        {
            return payload?.Body ?? string.Empty;
        }

        /// <summary>
        /// Creates a test email payload with correct property structure
        /// </summary>
        protected EmailPayload CreateTestEmailPayload(string gatewayName)
        {
            return new EmailPayload
            {
                To = "<EMAIL>",
                From = "<EMAIL>",
                Subject = $"Test email from {gatewayName} gateway",
                Body = $"This is a test message from {gatewayName} gateway",
                IsHtml = false
            };
        }

        #endregion

        #region Push Payload Helper Methods

        /// <summary>
        /// Gets the device token from PushPayload.DeviceToken
        /// </summary>
        protected string GetPushDeviceToken(PushPayload payload)
        {
            return payload?.DeviceToken ?? string.Empty;
        }

        /// <summary>
        /// Gets device tokens as a list (compatibility helper)
        /// </summary>
        protected List<string> GetPushDeviceTokens(PushPayload payload)
        {
            // PushPayload has DeviceToken (single), but some implementations expect DeviceTokens (list)
            if (!string.IsNullOrEmpty(payload?.DeviceToken))
            {
                return new List<string> { payload.DeviceToken };
            }
            return new List<string>();
        }

        /// <summary>
        /// Gets the push title from PushPayload.Title
        /// </summary>
        protected string GetPushTitle(PushPayload payload)
        {
            return payload?.Title ?? string.Empty;
        }

        /// <summary>
        /// Gets the push body from PushPayload.Body
        /// </summary>
        protected string GetPushBody(PushPayload payload)
        {
            return payload?.Body ?? string.Empty;
        }

        /// <summary>
        /// Creates a test push payload with correct property structure
        /// </summary>
        protected PushPayload CreateTestPushPayload(string gatewayName)
        {
            return new PushPayload
            {
                DeviceToken = "test_device_token",
                Title = $"Test from {gatewayName}",
                Body = $"Test push notification from {gatewayName} gateway",
                Data = new Dictionary<string, string>()
            };
        }

        #endregion

        #region MessengerApp Payload Helper Methods

        /// <summary>
        /// Gets the recipient ID from MessengerAppPayload.RecipientId
        /// </summary>
        protected string GetMessengerRecipientId(MessengerAppPayload payload)
        {
            return payload?.RecipientId ?? string.Empty;
        }

        /// <summary>
        /// Gets the text content from MessengerAppPayload.Text
        /// </summary>
        protected string GetMessengerText(MessengerAppPayload payload)
        {
            return payload?.Text ?? string.Empty;
        }

        /// <summary>
        /// Gets the platform from MessengerAppPayload.Platform
        /// </summary>
        protected string GetMessengerPlatform(MessengerAppPayload payload)
        {
            return payload?.Platform ?? string.Empty;
        }

        /// <summary>
        /// Creates a test messenger app payload with correct property structure
        /// </summary>
        protected MessengerAppPayload CreateTestMessengerAppPayload(string gatewayName)
        {
            return new MessengerAppPayload
            {
                Platform = gatewayName.ToLower(),
                RecipientId = "test_recipient",
                MessageType = "text",
                Text = $"Test message from {gatewayName} gateway"
            };
        }

        #endregion

        #region Gateway Configuration Helper Methods

        /// <summary>
        /// Creates a successful GatewayConfigurationResult
        /// </summary>
        protected GatewayConfigurationResult CreateSuccessConfigurationResult(Dictionary<string, object> configuration, string gatewayName = "")
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Configuration = configuration ?? new Dictionary<string, object>(),
                ErrorMessage = null,
                LastUpdated = DateTime.UtcNow,
                GatewayName = gatewayName ?? _gatewayName,
                ValidationErrors = new List<string>()
            };
        }

        /// <summary>
        /// Creates an error GatewayConfigurationResult
        /// </summary>
        protected GatewayConfigurationResult CreateErrorConfigurationResult(string errorMessage, string gatewayName = null)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                Configuration = new Dictionary<string, object>(),
                ErrorMessage = errorMessage,
                LastUpdated = null,
                GatewayName = gatewayName ?? _gatewayName,
                ValidationErrors = new List<string> { errorMessage }
            };
        }

        /// <summary>
        /// Creates a successful GatewayTemplateResult
        /// </summary>
        protected GatewayTemplateResult CreateSuccessTemplateResult(GatewayTemplate template, string gatewayName = null)
        {
            return new GatewayTemplateResult
            {
                IsSuccess = true,
                Template = template,
                ErrorMessage = null,
                GatewayName = gatewayName ?? _gatewayName
            };
        }

        /// <summary>
        /// Creates an error GatewayTemplateResult
        /// </summary>
        protected GatewayTemplateResult CreateErrorTemplateResult(string errorMessage, string gatewayName = null)
        {
            return new GatewayTemplateResult
            {
                IsSuccess = false,
                Template = null,
                ErrorMessage = errorMessage,
                GatewayName = gatewayName ?? _gatewayName
            };
        }

        /// <summary>
        /// Creates a successful GatewayTemplatesResult
        /// </summary>
        protected GatewayTemplatesResult CreateSuccessTemplatesResult(List<GatewayTemplate> templates, string gatewayName = null)
        {
            return new GatewayTemplatesResult
            {
                IsSuccess = true,
                Templates = templates ?? new List<GatewayTemplate>(),
                ErrorMessage = null,
                TotalCount = templates?.Count ?? 0,
                GatewayName = gatewayName ?? _gatewayName
            };
        }

        /// <summary>
        /// Creates an error GatewayTemplatesResult
        /// </summary>
        protected GatewayTemplatesResult CreateErrorTemplatesResult(string errorMessage, string gatewayName = null)
        {
            return new GatewayTemplatesResult
            {
                IsSuccess = false,
                Templates = new List<GatewayTemplate>(),
                ErrorMessage = errorMessage,
                TotalCount = 0,
                GatewayName = gatewayName ?? _gatewayName
            };
        }

        #endregion

        #region Gateway Retry Configuration Helper Methods

        /// <summary>
        /// Creates a default retry configuration with proper structure
        /// </summary>
        protected GatewayRetryConfiguration CreateDefaultRetryConfiguration()
        {
            return new GatewayRetryConfiguration
            {
                RetryPolicy = new GatewayRetryPolicy
                {
                    IsEnabled = true,
                    MaxRetries = 3,
                    InitialDelaySeconds = 1,
                    MaxDelaySeconds = 60,
                    BackoffMultiplier = 2.0,
                    UseExponentialBackoff = true,
                    RetryableStatusCodes = new List<int> { 429, 500, 502, 503, 504 },
                    RetryableExceptions = new List<string> { "RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE" }
                },
                CircuitBreakerSettings = new Dictionary<string, object>(),
                RateLimitSettings = new Dictionary<string, object>()
            };
        }

        /// <summary>
        /// Gets retry policy settings from GatewayRetryConfiguration
        /// </summary>
        protected GatewayRetryPolicy GetRetryPolicy(GatewayRetryConfiguration config)
        {
            return config?.RetryPolicy ?? new GatewayRetryPolicy();
        }

        #endregion

        #region Template Helper Methods

        /// <summary>
        /// Creates a basic gateway template with correct property structure
        /// </summary>
        protected GatewayTemplate CreateGatewayTemplate(string id, string name, string content, string type, List<string> variables = null)
        {
            return new GatewayTemplate
            {
                Id = id,
                Name = name,
                Content = content,
                Type = type,
                Variables = variables ?? new List<string>(),
                Metadata = new Dictionary<string, object>()
            };
        }

        #endregion

        #region Test Helper Methods

        /// <summary>
        /// Creates a test result with proper error handling
        /// </summary>
        protected GatewayTestResult CreateTestResult(bool isSuccess, string message, TimeSpan responseTime, string testMessageId = null, string errorMessage = null)
        {
            return new GatewayTestResult
            {
                IsSuccess = isSuccess,
                Message = message,
                ResponseTime = responseTime,
                TestMessageId = testMessageId ?? Guid.NewGuid().ToString(),
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = _gatewayName,
                    ["TestType"] = "LiveTest",
                    ["Timestamp"] = DateTime.UtcNow
                },
                ErrorMessage = errorMessage
            };
        }

        #endregion
    }
}
