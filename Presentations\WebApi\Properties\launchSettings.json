{"profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "WebApi": {"commandName": "Project", "launchBrowser": true, "launchUrl": "scalar/v1", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "applicationUrl": "https://localhost:5001;http://localhost:5000"}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/scalar/v1", "environmentVariables": {"ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:61803", "sslPort": 44372}}}