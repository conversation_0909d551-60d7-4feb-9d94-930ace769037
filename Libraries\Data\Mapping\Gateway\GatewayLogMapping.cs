using Data.Mapping;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.DbEntities.Gateway;

namespace Data.Mapping.Gateway;

public class GatewayLogMapping : MappingEntityTypeConfiguration<GatewayLog>
{
    public override void Configure(EntityTypeBuilder<GatewayLog> builder)
    {
        builder.ToTable("GatewayLogs");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.MessageId)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.GatewayName)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Provider)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Operation)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Status)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.RequestPayload)
            .HasColumnType("jsonb");

        builder.Property(x => x.ResponseData)
            .HasColumnType("jsonb");

        builder.Property(x => x.ErrorMessage)
            .HasMaxLength(1000);

        builder.Property(x => x.ErrorCode)
            .HasMaxLength(100);

        builder.Property(x => x.Timestamp)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.Recipient)
            .HasMaxLength(200);

        builder.Property(x => x.Subject)
            .HasMaxLength(500);

        builder.Property(x => x.MessageType)
            .HasMaxLength(50);

        builder.Property(x => x.RetryCount)
            .HasDefaultValue(0);

        builder.Property(x => x.AdditionalInfo)
            .HasColumnType("jsonb");

        // Indexes
        builder.HasIndex(x => x.MessageId)
            .HasDatabaseName("IX_GatewayLogs_MessageId");

        builder.HasIndex(x => new { x.GatewayName, x.Provider })
            .HasDatabaseName("IX_GatewayLogs_GatewayName_Provider");

        builder.HasIndex(x => x.Status)
            .HasDatabaseName("IX_GatewayLogs_Status");

        builder.HasIndex(x => x.Timestamp)
            .HasDatabaseName("IX_GatewayLogs_Timestamp");

        builder.HasIndex(x => x.Operation)
            .HasDatabaseName("IX_GatewayLogs_Operation");

        builder.HasIndex(x => x.Recipient)
            .HasDatabaseName("IX_GatewayLogs_Recipient");
    }
}
