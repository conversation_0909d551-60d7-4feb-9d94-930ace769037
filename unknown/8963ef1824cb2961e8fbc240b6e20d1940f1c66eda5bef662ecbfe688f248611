// Global using directives

global using System;
global using System.Collections.Generic;
global using System.Globalization;
global using System.Net;
global using System.Net.Sockets;
global using System.Threading;
global using System.Threading.Tasks;
global using Core.Exceptions;
global using Core.Interfaces;
global using Core.Services;
global using Data.Contexts;
global using Data.Repos;
global using Data.UnitOfWork;
global using MailKit.Net.Smtp;
global using MailKit.Security;
global using Microsoft.EntityFrameworkCore;
global using Microsoft.Extensions.Logging;
global using Microsoft.Extensions.Options;
global using MimeKit;
global using Models.DTOs.Email;
global using Models.Settings;
global using Services.Interfaces;