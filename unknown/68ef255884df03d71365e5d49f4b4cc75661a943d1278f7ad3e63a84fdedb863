namespace Core.Interfaces;

public interface IGatewayMessageService<TPayload,TResult,TScheduleResult> : IMessageSender<TPayload, TResult>, IMessageScheduler<TPayload, TScheduleResult>
{
    Task<TResult> ResendAsync(string originalMessageId, CancellationToken cancellationToken = default);
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
}

public interface IAdminMetricsService<TConfiguration, TStatusReport, TUsageMetrics, TErrorLog, TPerformance,TSla,TLatency,TTraffic,TAnomaly,TReport,TRetry,TImpact,TOptions,TGranularity> 
{
    Task<IReadOnlyList<TConfiguration>> GetConfigurationsAsync(CancellationToken cancellationToken = default);
    Task UpdateConfigurationAsync(TConfiguration setting, CancellationToken cancellationToken = default);
    Task<TStatusReport> GetStatusReportAsync(CancellationToken cancellationToken = default);
    Task<TUsageMetrics> GetUsageMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
     Task<IReadOnlyList<TErrorLog>> GetErrorLogAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
    Task<TPerformance> GetPerformanceSnapshotAsync(TimeSpan resolution, CancellationToken cancellationToken = default);
    Task<TSla> GetSlaReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
    Task<TLatency> GetLatencyMetricsAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<TTraffic>> GetTrafficTrendsAsync(TGranularity granularity, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<TAnomaly>> GetAnomalyReportAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
    Task<TReport> GenerateMetricsReportAsync(TOptions options, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<TRetry>> GetRetryHistoryAsync(DateTimeOffset from, DateTimeOffset to, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<TImpact>> GetChangeImpactHistoryAsync(CancellationToken cancellationToken = default);
}