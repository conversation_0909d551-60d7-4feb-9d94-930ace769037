using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Interfaces
{
    public interface IMessageService<TPayload, TResult>
    {
        Task<TResult> SendAsync(TPayload payload, CancellationToken cancellationToken = default);
        Task<IReadOnlyList<TResult>> SendBulkAsync(IEnumerable<TPayload> payloads, CancellationToken cancellationToken = default);
        Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
        Task<TResult> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
    }
}
