using Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Models.DbEntities;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;

namespace Data.Repos;

public class LoginLogRepository : GenericRepository<LoginLog>, ILoginLogRepository
{
    private readonly ApplicationDbContext _context;

    public LoginLogRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<List<LoginLog>> GetByUserEmailAsync(string email)
    {
        return await _context.Set<LoginLog>()
            .Where(x => x.UserEmail.Equals(email))
            .OrderByDescending(x => x.LoginTime)
            .ToListAsync();
    }

    public async Task AddAsync(LoginLog loginLog)
    {
        await _context.Set<LoginLog>().AddAsync(loginLog);
        await _context.SaveChangesAsync();
    }

    public async Task<bool> ExistsForUserAsync(string email)
    {
        return await _context.Set<LoginLog>()
            .AnyAsync(x => x.UserEmail.Equals(email));
    }
}
