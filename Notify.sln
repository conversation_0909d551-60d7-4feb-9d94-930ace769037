﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 16
VisualStudioVersion = 16.0.30225.117
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{31F7550D-120E-4361-B905-ADDA0539EDF7}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Libraries", "Libraries", "{6ED22C80-F37F-409C-B307-FE457E95DE58}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Presentations", "Presentations", "{97B0F4E2-C596-4450-8EB0-959AC0930C28}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Data", "Libraries\Data\Data.csproj", "{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Services", "Libraries\Services\Services.csproj", "{C68F7E2D-653D-4D74-8DB9-580CA2CE878E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Models", "Libraries\Models\Models.csproj", "{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "WebApi", "Presentations\WebApi\WebApi.csproj", "{*************-48CC-8B1C-195F59FFC24C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Identity", "Libraries\Identity\Identity.csproj", "{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Core", "Libraries\Core\Core.csproj", "{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Caching", "Libraries\Caching\Caching.csproj", "{C0A5E261-4D9F-410E-A2C6-112CD9D874C9}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Data.Mongo", "Libraries\Data.Mongo\Data.Mongo.csproj", "{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A}"
EndProject


Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{7AF09108-D53F-4C9B-B590-B2797800FAAA}"
	ProjectSection(SolutionItems) = preProject
		docker-compose.yml = docker-compose.yml
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5}.Release|Any CPU.Build.0 = Release|Any CPU
		{C68F7E2D-653D-4D74-8DB9-580CA2CE878E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C68F7E2D-653D-4D74-8DB9-580CA2CE878E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C68F7E2D-653D-4D74-8DB9-580CA2CE878E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C68F7E2D-653D-4D74-8DB9-580CA2CE878E}.Release|Any CPU.Build.0 = Release|Any CPU
		{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5}.Release|Any CPU.Build.0 = Release|Any CPU
		{*************-48CC-8B1C-195F59FFC24C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{*************-48CC-8B1C-195F59FFC24C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{*************-48CC-8B1C-195F59FFC24C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{*************-48CC-8B1C-195F59FFC24C}.Release|Any CPU.Build.0 = Release|Any CPU
		{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3}.Release|Any CPU.Build.0 = Release|Any CPU
		{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E}.Release|Any CPU.Build.0 = Release|Any CPU
		{C0A5E261-4D9F-410E-A2C6-112CD9D874C9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C0A5E261-4D9F-410E-A2C6-112CD9D874C9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C0A5E261-4D9F-410E-A2C6-112CD9D874C9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C0A5E261-4D9F-410E-A2C6-112CD9D874C9}.Release|Any CPU.Build.0 = Release|Any CPU
		{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{6ED22C80-F37F-409C-B307-FE457E95DE58} = {31F7550D-120E-4361-B905-ADDA0539EDF7}
		{97B0F4E2-C596-4450-8EB0-959AC0930C28} = {31F7550D-120E-4361-B905-ADDA0539EDF7}
		{B3DC48BE-3D33-4DF1-B8F0-9D3791FBC4F5} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{C68F7E2D-653D-4D74-8DB9-580CA2CE878E} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{61F060ED-F5FD-4A36-A0BE-9E29F21A46D5} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{*************-48CC-8B1C-195F59FFC24C} = {97B0F4E2-C596-4450-8EB0-959AC0930C28}
		{35A449C0-D3C1-4A9C-AA0D-7FA767B245F3} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{83DCD3AE-695F-4362-92DC-E7A43A3ECB5E} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{C0A5E261-4D9F-410E-A2C6-112CD9D874C9} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
		{D4E5F6A7-B8C9-4D0A-9E1B-2F3C4D5E6F7A} = {6ED22C80-F37F-409C-B307-FE457E95DE58}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {9357748C-EABB-403D-A2D8-E745B257A0F0}
	EndGlobalSection
EndGlobal
