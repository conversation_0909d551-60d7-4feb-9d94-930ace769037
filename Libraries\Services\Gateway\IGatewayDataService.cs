using Models.DbEntities.Gateway;
using Models.DTOs.Gateway;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Services.Gateway;

/// <summary>
/// Service for managing gateway data persistence and retrieval
/// </summary>
public interface IGatewayDataService
{
    // Configuration Management
    Task<GatewayConfiguration?> GetGatewayConfigurationAsync(string gatewayName, string provider);
    Task<GatewayConfiguration> SaveGatewayConfigurationAsync(string gatewayName, string gatewayType, string provider, Dictionary<string, string> configuration, string? userId = null);
    Task<bool> UpdateGatewayConfigurationAsync(int configurationId, Dictionary<string, string> configuration, string? userId = null);
    Task<List<GatewayConfiguration>> GetGatewaysByTypeAsync(string gatewayType);
    Task<bool> SetDefaultGatewayAsync(int configurationId, string gatewayType);

    // Template Management
    Task<List<Models.DTOs.Gateway.GatewayTemplate>> GetTemplatesAsync(int gatewayConfigurationId);
    Task<Models.DTOs.Gateway.GatewayTemplate> SaveTemplateAsync(int gatewayConfigurationId, Models.DTOs.Gateway.GatewayTemplate template, string? userId = null);
    Task<bool> DeleteTemplateAsync(int gatewayConfigurationId, string templateId);
    Task<Models.DTOs.Gateway.GatewayTemplate?> GetTemplateAsync(int gatewayConfigurationId, string templateId);

    // Logging
    Task LogMessageAsync(string messageId, string gatewayName, string provider, string operation, string status, 
        object? requestPayload = null, object? responseData = null, string? errorMessage = null, 
        string? errorCode = null, double? latencyMs = null, string? recipient = null, 
        string? subject = null, string? messageType = null, int retryCount = 0, 
        Dictionary<string, object>? additionalInfo = null, int? gatewayConfigurationId = null);

    Task<List<GatewayLog>> GetMessageLogsAsync(string messageId);
    Task<List<GatewayLog>> GetGatewayLogsAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayLog>> GetFailedMessagesAsync(DateTime? startDate = null, DateTime? endDate = null);

    // Metrics
    Task SaveMetricAsync(string gatewayName, string provider, string metricType, string period, 
        DateTime periodStart, DateTime periodEnd, long totalMessages = 0, long successfulMessages = 0, 
        long failedMessages = 0, double averageLatencyMs = 0, Dictionary<string, object>? metricData = null, 
        int? gatewayConfigurationId = null);

    Task<List<GatewayMetric>> GetMetricsAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null);
    Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);

    // Real-time data for gateway interfaces
    Task<GatewayConfigurationResult> GetConfigurationResultAsync(string gatewayName, string provider);
    Task<GatewayConfigurationResult> UpdateConfigurationResultAsync(string gatewayName, string provider, Dictionary<string, string> configuration, string? userId = null);
    Task<GatewayTemplatesResult> GetTemplatesResultAsync(string gatewayName, string provider);
    Task<GatewayTemplateResult> SaveTemplateResultAsync(string gatewayName, string provider, Models.DTOs.Gateway.GatewayTemplate template, string? userId = null);
    Task<GatewayTemplateResult> DeleteTemplateResultAsync(string gatewayName, string provider, string templateId);

    // Analytics and reporting
    Task<Dictionary<string, long>> GetMessageCountsByStatusAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);
    Task<Dictionary<string, double>> GetLatencyStatsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);
    Task<List<GatewayLog>> GetRecentLogsAsync(string gatewayName, string provider, int count = 100);
}
