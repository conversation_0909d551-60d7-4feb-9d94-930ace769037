﻿namespace Models.Enums;

public enum Roles
{
    SuperAdmin,
    Admin,
    Moderator,
    Basic
}

public enum Permissions
{
    ManageUsers,
    ManageRoles,
    ManageProviders,
    ManageTemplates,
    ViewMetrics,
    SendMessages
}

public enum GatewayProviderTypes
{
    Proviver_Email,
    Proviver_Sms,
    Proviver_Push,
    Proviver_MessengerApp,
    Proviver_Webhook,
    Proviver_Storage

}

public enum ProviderFeatures
{
    Feature_Text,
    Feature_Media,
    Feature_Templates,
    Feature_Interactive,
    Feature_Location,
    Feature_TwoWay,
    Feature_Bulk,
    Feature_DeliveryReceipts,
    Feature_ReadReceipts,
    Feature_Segmentation,
    Feature_ABTesting,
    Feature_AIBasedPersonalization,
    Feature_AdvancedSecurity,
    Feature_ComplianceReporting,
    Feature_AuditLogging,
    Feature_AdvancedMonitoring,
    Feature_SLISLO,
    Feature_AdvancedDeployment,
    Feature_CanaryDeployment,
    Feature_BlueGreenDeployment,
    Feature_ChaosEngineering,
    Feature_Observability,
    Feature_AdvancedCaching,
    Feature_AdvancedDatabase,
    Feature_AdvancedNetworking
   
}

