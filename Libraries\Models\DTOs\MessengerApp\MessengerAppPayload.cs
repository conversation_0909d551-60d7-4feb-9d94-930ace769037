#nullable enable
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Models.DTOs.MessengerApp;

/// <summary>
/// Messenger app message payload (WhatsApp, Facebook Messenger, Telegram)
/// </summary>
public class MessengerAppPayload
{
    /// <summary>
    /// Target platform (whatsapp, facebook_messenger, telegram)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Platform { get; set; } = string.Empty;

    /// <summary>
    /// Recipient identifier (phone number for WhatsApp, user ID for others)
    /// </summary>
    [Required]
    [StringLength(100)]
    public string RecipientId { get; set; } = string.Empty;

    /// <summary>
    /// Message type (text, image, document, audio, video, template, interactive)
    /// </summary>
    [Required]
    [StringLength(50)]
    public string MessageType { get; set; } = "text";

    /// <summary>
    /// Text content for text messages
    /// </summary>
    [StringLength(4096)]
    public string? Text { get; set; }

    /// <summary>
    /// Media content for media messages
    /// </summary>
    public MessengerMediaContent? Media { get; set; }

    /// <summary>
    /// Template message content
    /// </summary>
    public MessengerTemplateContent? Template { get; set; }

    /// <summary>
    /// Interactive message content (buttons, lists, etc.)
    /// </summary>
    public MessengerInteractiveContent? Interactive { get; set; }

    /// <summary>
    /// Location content
    /// </summary>
    public MessengerLocationContent? Location { get; set; }

    /// <summary>
    /// Contact content
    /// </summary>
    public MessengerContactContent? Contact { get; set; }

    /// <summary>
    /// Message priority (low, normal, high)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Custom data payload
    /// </summary>
    public Dictionary<string, object>? CustomData { get; set; }

    /// <summary>
    /// Conversation context
    /// </summary>
    public MessengerConversationContext? Context { get; set; }

    /// <summary>
    /// Delivery options
    /// </summary>
    public MessengerDeliveryOptions? DeliveryOptions { get; set; }

    /// <summary>
    /// Tags for categorization
    /// </summary>
    public List<string>? Tags { get; set; }
}

/// <summary>
/// Media content for messenger messages
/// </summary>
public class MessengerMediaContent
{
    /// <summary>
    /// Media type (image, document, audio, video, sticker)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Media URL or file path
    /// </summary>
    [StringLength(500)]
    public string? Url { get; set; }

    /// <summary>
    /// Media file data (base64 encoded)
    /// </summary>
    public byte[]? Data { get; set; }

    /// <summary>
    /// Media filename
    /// </summary>
    [StringLength(255)]
    public string? Filename { get; set; }

    /// <summary>
    /// Media caption
    /// </summary>
    [StringLength(1024)]
    public string? Caption { get; set; }

    /// <summary>
    /// Media MIME type
    /// </summary>
    [StringLength(100)]
    public string? MimeType { get; set; }
}

/// <summary>
/// Template message content
/// </summary>
public class MessengerTemplateContent
{
    /// <summary>
    /// Template name/ID
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Template language code
    /// </summary>
    [Required]
    [StringLength(10)]
    public string Language { get; set; } = "en";

    /// <summary>
    /// Template parameters
    /// </summary>
    public List<MessengerTemplateParameter>? Parameters { get; set; }

    /// <summary>
    /// Template components (header, body, footer, buttons)
    /// </summary>
    public Dictionary<string, object>? Components { get; set; }
}

/// <summary>
/// Template parameter
/// </summary>
public class MessengerTemplateParameter
{
    /// <summary>
    /// Parameter type (text, currency, date_time, image, document, video)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Parameter value
    /// </summary>
    [Required]
    public object Value { get; set; } = string.Empty;
}

/// <summary>
/// Interactive message content
/// </summary>
public class MessengerInteractiveContent
{
    /// <summary>
    /// Interactive type (button, list, product, product_list)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Header content
    /// </summary>
    public MessengerInteractiveHeader? Header { get; set; }

    /// <summary>
    /// Body text
    /// </summary>
    [StringLength(1024)]
    public string? Body { get; set; }

    /// <summary>
    /// Footer text
    /// </summary>
    [StringLength(60)]
    public string? Footer { get; set; }

    /// <summary>
    /// Action buttons or list items
    /// </summary>
    public List<MessengerInteractiveAction>? Actions { get; set; }
}

/// <summary>
/// Interactive header
/// </summary>
public class MessengerInteractiveHeader
{
    /// <summary>
    /// Header type (text, image, video, document)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Header text
    /// </summary>
    [StringLength(60)]
    public string? Text { get; set; }

    /// <summary>
    /// Header media
    /// </summary>
    public MessengerMediaContent? Media { get; set; }
}

/// <summary>
/// Interactive action (button or list item)
/// </summary>
public class MessengerInteractiveAction
{
    /// <summary>
    /// Action type (reply, url, call, copy_code)
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Action title/text
    /// </summary>
    [Required]
    [StringLength(20)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Action ID or payload
    /// </summary>
    [StringLength(256)]
    public string? Id { get; set; }

    /// <summary>
    /// URL for url actions
    /// </summary>
    [StringLength(500)]
    public string? Url { get; set; }

    /// <summary>
    /// Phone number for call actions
    /// </summary>
    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Description for list items
    /// </summary>
    [StringLength(72)]
    public string? Description { get; set; }
}

/// <summary>
/// Location content
/// </summary>
public class MessengerLocationContent
{
    /// <summary>
    /// Latitude
    /// </summary>
    [Required]
    public double Latitude { get; set; }

    /// <summary>
    /// Longitude
    /// </summary>
    [Required]
    public double Longitude { get; set; }

    /// <summary>
    /// Location name
    /// </summary>
    [StringLength(100)]
    public string? Name { get; set; }

    /// <summary>
    /// Location address
    /// </summary>
    [StringLength(200)]
    public string? Address { get; set; }
}

/// <summary>
/// Contact content
/// </summary>
public class MessengerContactContent
{
    /// <summary>
    /// Contact name
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Phone numbers
    /// </summary>
    public List<MessengerContactPhone>? Phones { get; set; }

    /// <summary>
    /// Email addresses
    /// </summary>
    public List<MessengerContactEmail>? Emails { get; set; }

    /// <summary>
    /// Organization
    /// </summary>
    [StringLength(100)]
    public string? Organization { get; set; }
}

/// <summary>
/// Contact phone number
/// </summary>
public class MessengerContactPhone
{
    [StringLength(20)]
    public string? Phone { get; set; }

    [StringLength(20)]
    public string? Type { get; set; } = "MOBILE";
}

/// <summary>
/// Contact email
/// </summary>
public class MessengerContactEmail
{
    [StringLength(100)]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? Type { get; set; } = "WORK";
}

/// <summary>
/// Conversation context
/// </summary>
public class MessengerConversationContext
{
    /// <summary>
    /// Conversation ID
    /// </summary>
    [StringLength(100)]
    public string? ConversationId { get; set; }

    /// <summary>
    /// Message being replied to
    /// </summary>
    [StringLength(100)]
    public string? ReplyToMessageId { get; set; }

    /// <summary>
    /// Conversation tags
    /// </summary>
    public List<string>? ConversationTags { get; set; }

    /// <summary>
    /// User context data
    /// </summary>
    public Dictionary<string, object>? UserContext { get; set; }
}

/// <summary>
/// Delivery options
/// </summary>
public class MessengerDeliveryOptions
{
    /// <summary>
    /// Request delivery receipt
    /// </summary>
    public bool RequestDeliveryReceipt { get; set; } = true;

    /// <summary>
    /// Request read receipt
    /// </summary>
    public bool RequestReadReceipt { get; set; } = true;

    /// <summary>
    /// Message TTL in seconds
    /// </summary>
    public int? TimeToLiveSeconds { get; set; }

    /// <summary>
    /// Notification type (REGULAR, SILENT_PUSH, NO_PUSH)
    /// </summary>
    [StringLength(20)]
    public string NotificationType { get; set; } = "REGULAR";
}
