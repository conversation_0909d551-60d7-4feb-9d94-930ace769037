using System;
using System.Collections.Generic;

namespace Models.DTOs.SMS
{
    /// <summary>
    /// Result of phone number validation
    /// </summary>
    public class PhoneValidationResult
    {
        /// <summary>
        /// The phone number that was validated
        /// </summary>
        public string PhoneNumber { get; set; } = string.Empty;

        /// <summary>
        /// Whether the phone number is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Formatted phone number in international format
        /// </summary>
        public string? FormattedNumber { get; set; }

        /// <summary>
        /// Country code (e.g., "US", "GB", "DE")
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// Country name
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// Phone number type (mobile, landline, voip, etc.)
        /// </summary>
        public string? NumberType { get; set; }

        /// <summary>
        /// Mobile carrier/operator name
        /// </summary>
        public string? Carrier { get; set; }

        /// <summary>
        /// Whether the number is reachable for SMS
        /// </summary>
        public bool? IsSmsCapable { get; set; }

        /// <summary>
        /// Whether the number is currently active
        /// </summary>
        public bool? IsActive { get; set; }

        /// <summary>
        /// Confidence score (0-100) if available
        /// </summary>
        public int? ConfidenceScore { get; set; }

        /// <summary>
        /// Detailed validation information
        /// </summary>
        public Dictionary<string, object> ValidationDetails { get; set; } = new();

        /// <summary>
        /// Error message if validation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// When the validation was performed
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gateway that performed the validation
        /// </summary>
        public string? GatewayName { get; set; }

        /// <summary>
        /// Raw validation response from the provider
        /// </summary>
        public object? ProviderResponse { get; set; }
    }
}
