﻿using Models.DbEntities;
using Identity.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Account;
using Services.Interfaces;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Account management endpoints for user authentication, registration, and password management
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class AccountController : ControllerBase
{
    private readonly IAccountService _accountService;
    private readonly ILoginLogService _loginLogService;
    public AccountController(IAccountService accountService, ILoginLogService loginLogService)
    {
        _accountService = accountService;
        _loginLogService = loginLogService;
    }

    /// <summary>
    /// Authenticate user and return JWT token
    /// </summary>
    /// <param name="request">User credentials (email and password)</param>
    /// <returns>JWT token and user information</returns>
    /// <response code="200">Authentication successful - returns JWT token</response>
    /// <response code="400">Invalid credentials or validation errors</response>
    [HttpPost("authenticate")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> AuthenticateAsync(AuthenticationRequest request)
    {
        //auth
        var result = await _accountService.AuthenticateAsync(request);
        if (result.Errors == null || !result.Errors.Any())
        {
            //mongo usage example
            var log = new LoginLog()
            {
                LoginTime = DateTime.UtcNow,
                UserEmail = request.Email
            };
            await _loginLogService.Add(log);
        }
        return Ok(result);
    }

    /// <summary>
    /// Register a new user account
    /// </summary>
    /// <param name="request">User registration details</param>
    /// <returns>Registration result with confirmation details</returns>
    /// <response code="200">Registration successful</response>
    /// <response code="400">Validation errors or registration failed</response>
    [HttpPost("register")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> RegisterAsync(RegisterRequest request)
    {
        var uri = $"{Request.Scheme}://{Request.Host.Value}";
        return Ok(await _accountService.RegisterAsync(request, uri));
    }

    /// <summary>
    /// Confirm user email address
    /// </summary>
    /// <param name="userId">User ID from confirmation email</param>
    /// <param name="code">Confirmation code from email</param>
    /// <returns>Email confirmation result</returns>
    /// <response code="200">Email confirmed successfully</response>
    /// <response code="400">Invalid confirmation code or user ID</response>
    [HttpGet("confirm-email")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> ConfirmEmailAsync([FromQuery] string userId, [FromQuery] string code)
    {
        return Ok(await _accountService.ConfirmEmailAsync(userId, code));
    }

    /// <summary>
    /// Request password reset for user account
    /// </summary>
    /// <param name="request">Forgot password request with email address</param>
    /// <returns>Password reset confirmation</returns>
    /// <response code="200">Password reset email sent successfully</response>
    /// <response code="400">Invalid email or request failed</response>
    [HttpPost("forgot-password")]
    [ProducesResponseType(200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> ForgotPasswordAsync([FromBody] ForgotPasswordRequest request)
    {
        var uri = $"{Request.Scheme}://{Request.Host.Value}";
        await _accountService.ForgotPasswordAsync(request, uri);
        return Ok();
    }

    /// <summary>
    /// Reset user password using reset token
    /// </summary>
    /// <param name="request">Reset password request with token and new password</param>
    /// <returns>Password reset result</returns>
    /// <response code="200">Password reset successfully</response>
    /// <response code="400">Invalid token or reset failed</response>
    [HttpPost("reset-password")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> ResetPasswordAsync([FromBody] ResetPasswordRequest request)
    {
        return Ok(await _accountService.ResetPasswordAsync(request));
    }

    /// <summary>
    /// Refresh JWT access token using refresh token
    /// </summary>
    /// <param name="request">Refresh token request</param>
    /// <returns>New JWT access token</returns>
    /// <response code="200">Token refreshed successfully</response>
    /// <response code="400">Invalid refresh token</response>
    [HttpPost("refreshtoken")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> RefreshTokenAsync([FromBody] RefreshTokenRequest request)
    {
        return Ok(await _accountService.RefreshTokenAsync(request));
    }

    /// <summary>
    /// Logout user and invalidate tokens
    /// </summary>
    /// <param name="userEmail">User email address to logout</param>
    /// <returns>Logout confirmation</returns>
    /// <response code="200">User logged out successfully</response>
    /// <response code="400">Logout failed</response>
    [HttpGet("logout")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> LogoutAsync([FromQuery] string userEmail)
    {
        return Ok(await _accountService.LogoutAsync(userEmail));
    }

    private string GenerateIPAddress()
    {
        if (Request.Headers.ContainsKey("X-Forwarded-For"))
            return Request.Headers["X-Forwarded-For"];
        else
            return HttpContext.Connection.RemoteIpAddress.MapToIPv4().ToString();
    }
}