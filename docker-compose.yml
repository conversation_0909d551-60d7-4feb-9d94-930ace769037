# =========================
# Docker Compose for Notify Service API
# Production-ready configuration with monitoring
# =========================

version: '3.8'

services:
  # =========================
  # PostgreSQL Database
  # =========================
  postgres:
    image: postgres:15-alpine
    container_name: notify-postgres
    environment:
      POSTGRES_DB: NotifyDb
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-defaultpassword}
      POSTGRES_INITDB_ARGS: "--encoding=UTF8 --locale=C"
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
      - ./scripts/postgres-backup:/backup
    networks:
      - notify-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d NotifyDb"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # =========================
  # Redis Cache
  # =========================
  redis:
    image: redis:7-alpine
    container_name: notify-redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-}
    ports:
      - "${REDIS_PORT:-6379}:6379"
    volumes:
      - redis_data:/data
      - ./config/redis.conf:/usr/local/etc/redis/redis.conf
    networks:
      - notify-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # =========================
  # Notify Service API
  # =========================
  api:
    build:
      context: .
      dockerfile: Dockerfile.production
      args:
        BUILD_CONFIGURATION: Release
    container_name: notify-api
    environment:
      # ASP.NET Core Configuration
      - ASPNETCORE_ENVIRONMENT=${ASPNETCORE_ENVIRONMENT:-Production}
      - ASPNETCORE_URLS=http://+:80
      - ASPNETCORE_FORWARDEDHEADERS_ENABLED=true

      # Database Configuration
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=NotifyDb;Username=postgres;Password=${POSTGRES_PASSWORD:-defaultpassword};Include Error Detail=true;
      - ConnectionStrings__IdentityConnection=Host=postgres;Database=NotifyIdentityDb;Username=postgres;Password=${POSTGRES_PASSWORD:-defaultpassword};Include Error Detail=true;

      # Redis Configuration
      - RedisSettings__RedisConnectionString=redis:6379
      - RedisSettings__CacheTime=30
      - RedisSettings__RedisDatabaseId=0

      # JWT Configuration
      - JWTSettings__Key=${JWT_SECRET_KEY}
      - JWTSettings__Issuer=NotifyServiceAPI
      - JWTSettings__Audience=NotifyServiceUsers
      - JWTSettings__DurationInMinutes=60

      # Email Configuration
      - MailSettings__EmailFrom=${MAIL_FROM:-<EMAIL>}
      - MailSettings__SmtpHost=${SMTP_HOST:-smtp.gmail.com}
      - MailSettings__SmtpPort=${SMTP_PORT:-587}
      - MailSettings__SmtpUser=${SMTP_USER}
      - MailSettings__SmtpPass=${SMTP_PASSWORD}
      - MailSettings__DisplayName=${MAIL_DISPLAY_NAME:-Notify Service}

      # Logging Configuration
      - Logging__LogLevel__Default=Information
      - Logging__LogLevel__Microsoft.AspNetCore=Warning
      - Logging__LogLevel__Microsoft.EntityFrameworkCore=Warning

      # Security
      - AllowedHosts=*
    ports:
      - "${API_PORT:-8080}:80"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - notify-network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
        reservations:
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "5"

# =========================
# Volumes
# =========================
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local

# =========================
# Networks
# =========================
networks:
  notify-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
