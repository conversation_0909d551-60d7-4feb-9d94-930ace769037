#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Models.DTOs.Notification;

/// <summary>
/// Universal notification payload that can be sent across multiple channels
/// </summary>
public class NotificationPayload
{
    /// <summary>
    /// Target recipients (user IDs, email addresses, phone numbers, device tokens)
    /// </summary>
    [Required]
    public List<string> Recipients { get; set; } = new();

    /// <summary>
    /// Notification title
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Notification message/content
    /// </summary>
    [Required]
    [StringLength(2000)]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Target channels (email, sms, push, inapp, webhook)
    /// </summary>
    [Required]
    public List<string> Channels { get; set; } = new();

    /// <summary>
    /// Notification type/category
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = "info";

    /// <summary>
    /// Priority level (low, normal, high, urgent)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Optional subject (for email)
    /// </summary>
    [StringLength(200)]
    public string? Subject { get; set; }

    /// <summary>
    /// Optional HTML content (for email)
    /// </summary>
    public string? HtmlContent { get; set; }

    /// <summary>
    /// Optional template ID to use
    /// </summary>
    [StringLength(100)]
    public string? TemplateId { get; set; }

    /// <summary>
    /// Template variables for substitution
    /// </summary>
    public Dictionary<string, string>? TemplateVariables { get; set; }

    /// <summary>
    /// Attachments (for email)
    /// </summary>
    public List<NotificationAttachment>? Attachments { get; set; }

    /// <summary>
    /// Custom data payload
    /// </summary>
    public Dictionary<string, object>? Data { get; set; }

    /// <summary>
    /// Scheduling options
    /// </summary>
    public NotificationScheduling? Scheduling { get; set; }

    /// <summary>
    /// Delivery options per channel
    /// </summary>
    public Dictionary<string, object>? ChannelOptions { get; set; }

    /// <summary>
    /// Tags for categorization and filtering
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Tracking and analytics options
    /// </summary>
    public NotificationTracking? Tracking { get; set; }

    /// <summary>
    /// Localization settings
    /// </summary>
    public NotificationLocalization? Localization { get; set; }
}

/// <summary>
/// Notification attachment
/// </summary>
public class NotificationAttachment
{
    [Required]
    [StringLength(255)]
    public string FileName { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string ContentType { get; set; } = string.Empty;

    [Required]
    public byte[] Content { get; set; } = Array.Empty<byte>();

    [StringLength(500)]
    public string? Url { get; set; }
}

/// <summary>
/// Notification scheduling options
/// </summary>
public class NotificationScheduling
{
    public DateTime? ScheduledTime { get; set; }
    public string? TimeZone { get; set; }
    public bool OptimalTiming { get; set; } = false;
    public Dictionary<string, object>? RecurrenceRule { get; set; }
}

/// <summary>
/// Notification tracking options
/// </summary>
public class NotificationTracking
{
    public bool TrackOpens { get; set; } = true;
    public bool TrackClicks { get; set; } = true;
    public bool TrackDelivery { get; set; } = true;
    public string? CampaignId { get; set; }
    public Dictionary<string, string>? CustomProperties { get; set; }
}

/// <summary>
/// Notification localization settings
/// </summary>
public class NotificationLocalization
{
    [StringLength(10)]
    public string? Language { get; set; }

    [StringLength(10)]
    public string? Region { get; set; }

    public Dictionary<string, string>? LocalizedContent { get; set; }
}
