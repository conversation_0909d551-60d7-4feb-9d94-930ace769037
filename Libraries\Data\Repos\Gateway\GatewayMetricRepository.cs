using Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Models.DbEntities.Gateway;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Data.Repos.Gateway;

public class GatewayMetricRepository : GenericRepository<GatewayMetric>, IGatewayMetricRepository
{
    private readonly ApplicationDbContext _context;

    public GatewayMetricRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<List<GatewayMetric>> GetByGatewayAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Set<GatewayMetric>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider);

        if (startDate.HasValue)
            query = query.Where(x => x.PeriodStart >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(x => x.PeriodEnd <= endDate.Value);

        return await query
            .OrderByDescending(x => x.PeriodStart)
            .ToListAsync();
    }

    public async Task<List<GatewayMetric>> GetByMetricTypeAsync(string metricType, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Set<GatewayMetric>()
            .Where(x => x.MetricType == metricType);

        if (startDate.HasValue)
            query = query.Where(x => x.PeriodStart >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(x => x.PeriodEnd <= endDate.Value);

        return await query
            .OrderByDescending(x => x.PeriodStart)
            .ToListAsync();
    }

    public async Task<List<GatewayMetric>> GetByPeriodAsync(string period, DateTime startDate, DateTime endDate)
    {
        return await _context.Set<GatewayMetric>()
            .Where(x => x.Period == period && x.PeriodStart >= startDate && x.PeriodEnd <= endDate)
            .OrderByDescending(x => x.PeriodStart)
            .ToListAsync();
    }

    public async Task<GatewayMetric?> GetMetricAsync(string gatewayName, string provider, string metricType, string period, DateTime periodStart)
    {
        return await _context.Set<GatewayMetric>()
            .FirstOrDefaultAsync(x => x.GatewayName == gatewayName && 
                                    x.Provider == provider && 
                                    x.MetricType == metricType && 
                                    x.Period == period && 
                                    x.PeriodStart == periodStart);
    }

    public async Task SaveMetricAsync(GatewayMetric metric)
    {
        await _context.Set<GatewayMetric>().AddAsync(metric);
        await _context.SaveChangesAsync();
    }

    public async Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        var metrics = await _context.Set<GatewayMetric>()
            .Where(x => x.GatewayName == gatewayName && 
                       x.Provider == provider && 
                       x.PeriodStart >= startDate && 
                       x.PeriodEnd <= endDate)
            .ToListAsync();

        if (!metrics.Any())
            return new Dictionary<string, object>();

        var totalMessages = metrics.Sum(x => x.TotalMessages);
        var successfulMessages = metrics.Sum(x => x.SuccessfulMessages);
        var failedMessages = metrics.Sum(x => x.FailedMessages);
        var avgLatency = metrics.Where(x => x.AverageLatencyMs > 0).Average(x => x.AverageLatencyMs);

        return new Dictionary<string, object>
        {
            ["TotalMessages"] = totalMessages,
            ["SuccessfulMessages"] = successfulMessages,
            ["FailedMessages"] = failedMessages,
            ["SuccessRate"] = totalMessages > 0 ? (double)successfulMessages / totalMessages * 100 : 0,
            ["FailureRate"] = totalMessages > 0 ? (double)failedMessages / totalMessages * 100 : 0,
            ["AverageLatencyMs"] = avgLatency,
            ["DeliveredMessages"] = metrics.Sum(x => x.DeliveredMessages),
            ["ReadMessages"] = metrics.Sum(x => x.ReadMessages),
            ["BouncedMessages"] = metrics.Sum(x => x.BouncedMessages),
            ["ComplainedMessages"] = metrics.Sum(x => x.ComplainedMessages),
            ["MetricCount"] = metrics.Count,
            ["PeriodStart"] = startDate,
            ["PeriodEnd"] = endDate
        };
    }

    public async Task<List<GatewayMetric>> GetLatestMetricsAsync(string gatewayName, string provider, int count = 10)
    {
        return await _context.Set<GatewayMetric>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider)
            .OrderByDescending(x => x.PeriodStart)
            .Take(count)
            .ToListAsync();
    }
}
