# install-dependencies.ps1

# Install Chocolatey if it's not already installed
if (-not (Get-Command choco -ErrorAction SilentlyContinue)) {
    Write-Host "Chocolatey is not installed. Installing Chocolatey..."
    iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
    Write-Host "Chocolatey installed successfully."
} else {
    Write-Host "Chocolatey is already installed."
}

# Install PSProgress module
Install-Module -Name PSProgress -Force

# Install .NET SDK
$dotnetVersion = "5.0"  # Specify the .NET SDK version
choco install dotnet-sdk."$dotnetVersion" -y

# Install Node.js
$nodeVersion = "16" # Specify the Node.js version
choco install nodejs."$nodeVersion" -y

# Install Docker Desktop
choco install docker-desktop -y

# Install other dependencies (if any)
# Example: choco install dependency-name -y

Write-Host "Dependencies installation completed."
