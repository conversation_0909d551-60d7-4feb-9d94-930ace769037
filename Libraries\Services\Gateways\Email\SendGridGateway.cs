#nullable enable
using Models.DTOs.Email;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Libraries.Services.Gateways.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Gateways.Email;

/// <summary>
/// SendGrid email gateway implementation
/// </summary>
public class SendGridGateway : IMessageGateway<EmailPayload, EmailResult>, IAdminGateway, IMetricsGateway, ISchedulableGateway<EmailPayload, EmailScheduleResult>
{
    private readonly HttpClient _httpClient;
    private string _apiKey = string.Empty;
    private bool _isInitialized = false;

    public string ProviderName => "SendGrid";
    public bool IsEnabled => _isInitialized && !string.IsNullOrEmpty(_apiKey);

    public SendGridGateway(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        if (configuration.TryGetValue("ApiKey", out var apiKey))
            _apiKey = apiKey;

        // Set up API key authentication
        if (!string.IsNullOrEmpty(_apiKey))
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_apiKey}");
        }

        _isInitialized = true;
        await Task.CompletedTask;
    }

    public async Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            // Test by getting API key info
            var response = await _httpClient.GetAsync("https://api.sendgrid.com/v3/scopes", cancellationToken);
            
            return new GatewayHealthResult
            {
                IsHealthy = response.IsSuccessStatusCode,
                ResponseTime = DateTime.UtcNow - startTime,
                AdditionalInfo = new Dictionary<string, object>
                {
                    ["StatusCode"] = (int)response.StatusCode,
                    ["ApiKey"] = _apiKey.Substring(0, Math.Min(10, _apiKey.Length)) + "..."
                }
            };
        }
        catch (Exception ex)
        {
            return new GatewayHealthResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message,
                ResponseTime = DateTime.UtcNow - startTime
            };
        }
    }

    public GatewayCapabilities GetCapabilities()
    {
        return new GatewayCapabilities
        {
            SupportsBulkSending = true,
            SupportsScheduling = true,
            SupportsDeliveryReceipts = true,
            SupportsReadReceipts = true,
            SupportsTemplates = true,
            SupportsAttachments = true,
            SupportsRichContent = true,
            MaxMessageSize = 30 * 1024 * 1024, // 30MB
            MaxBulkSize = 1000,
            RateLimitPerMinute = 600,
            SupportedContentTypes = new List<string> { "text/plain", "text/html" },
            SupportedFeatures = new List<string> { "Templates", "Personalization", "Analytics", "A/B Testing", "Webhooks", "Suppressions" }
        };
    }

    public async Task<EmailResult> SendAsync(EmailPayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var sendGridPayload = ConvertToSendGridPayload(payload);
            var json = JsonSerializer.Serialize(sendGridPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.sendgrid.com/v3/mail/send", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                // SendGrid returns 202 with X-Message-Id header
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                
                return GatewayResultHelper.CreateEmailSuccessResult(payload, messageId, responseContent, 202, "SendGrid");
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<SendGridErrorResponse>(responseContent);
                return GatewayResultHelper.CreateEmailErrorResult(payload,
                    errorResponse?.errors?.FirstOrDefault()?.message ?? $"SendGrid API error: {response.StatusCode}",
                    (int)response.StatusCode, null);
            }
        }
        catch (Exception ex)
        {
            return GatewayResultHelper.CreateEmailErrorResult(payload, ex.Message, 500, null);
        }
    }

    public async Task<IReadOnlyList<EmailResult>> SendBulkAsync(IEnumerable<EmailPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<EmailResult>();
        var payloadList = payloads.ToList();

        try
        {
            // SendGrid supports bulk sending with personalizations
            var sendGridPayload = ConvertToBulkSendGridPayload(payloadList);
            var json = JsonSerializer.Serialize(sendGridPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.sendgrid.com/v3/mail/send", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                
                // Create results for each payload
                foreach (var payload in payloadList)
                {
                    results.Add(new EmailResult
                    {
                        MessageId = $"{messageId}_{results.Count}",
                        IsSuccess = true,
                        Recipient = payload.To,
                        Subject = payload.Subject,
                        SentAt = DateTime.UtcNow,
                        StatusCode = 202
                    });
                }
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<SendGridErrorResponse>(responseContent);
                
                // Create failed results for each payload
                foreach (var payload in payloadList)
                {
                    results.Add(new EmailResult
                    {
                        MessageId = Guid.NewGuid().ToString(),
                        IsSuccess = false,
                        ErrorMessage = errorResponse?.errors?.FirstOrDefault()?.message ?? $"SendGrid bulk API error: {response.StatusCode}",
                        Recipient = payload.To,
                        Subject = payload.Subject,
                        SentAt = DateTime.UtcNow,
                        StatusCode = (int)response.StatusCode
                    });
                }
            }
        }
        catch (Exception ex)
        {
            // Create exception results for each payload
            foreach (var payload in payloadList)
            {
                results.Add(new EmailResult
                {
                    MessageId = Guid.NewGuid().ToString(),
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Recipient = payload.To,
                    Subject = payload.Subject,
                    SentAt = DateTime.UtcNow,
                    StatusCode = 500
                });
            }
        }

        return results.AsReadOnly();
    }

    public async Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"https://api.sendgrid.com/v3/messages/{messageId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var message = JsonSerializer.Deserialize<SendGridMessageStatus>(responseContent);
                
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = ConvertSendGridStatus(message?.status ?? "unknown"),
                    SentAt = message?.last_event_time,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["Provider"] = ProviderName,
                        ["SendGridStatus"] = message?.status ?? "unknown",
                        ["Events"] = message?.events?.Length ?? 0
                    }
                };
            }
            else
            {
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = "unknown",
                    ErrorMessage = $"Failed to get status: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            return new MessageStatus
            {
                MessageId = messageId,
                Status = "error",
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<EmailScheduleResult> ScheduleMessageAsync(EmailPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        try
        {
            var sendGridPayload = ConvertToSendGridPayload(payload);
            sendGridPayload["send_at"] = ((DateTimeOffset)scheduledTime).ToUnixTimeSeconds();
            
            var json = JsonSerializer.Serialize(sendGridPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.sendgrid.com/v3/mail/send", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var messageId = response.Headers.GetValues("X-Message-Id").FirstOrDefault() ?? Guid.NewGuid().ToString();
                
                return new EmailScheduleResult
                {
                    ScheduledMessageId = messageId,
                    IsScheduled = true,
                    ToEmail = payload.To,
                    Subject = payload.Subject,
                    ScheduledTime = scheduledTime.DateTime,
                    Status = "scheduled",
                    Provider = ProviderName,
                    CanCancel = true,
                    CanModify = false
                };
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<SendGridErrorResponse>(responseContent);
                return new EmailScheduleResult
                {
                    ScheduledMessageId = Guid.NewGuid().ToString(),
                    IsScheduled = false,
                    ErrorMessage = errorResponse?.errors?.FirstOrDefault()?.message ?? $"SendGrid scheduling error: {response.StatusCode}",
                    ErrorCode = errorResponse?.errors?.FirstOrDefault()?.field ?? response.StatusCode.ToString(),
                    ToEmail = payload.To,
                    Subject = payload.Subject,
                    ScheduledTime = scheduledTime.DateTime,
                    Status = "failed",
                    Provider = ProviderName
                };
            }
        }
        catch (Exception ex)
        {
            return new EmailScheduleResult
            {
                ScheduledMessageId = Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = ex.Message,
                ErrorCode = "EXCEPTION",
                ToEmail = payload.To,
                Subject = payload.Subject,
                ScheduledTime = scheduledTime.DateTime,
                Status = "failed",
                Provider = ProviderName
            };
        }
    }

    public async Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"https://api.sendgrid.com/v3/user/scheduled_sends/{scheduledMessageId}", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<EmailScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, EmailPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default)
    {
        // SendGrid doesn't support updating scheduled messages, need to cancel and reschedule
        var cancelled = await CancelScheduledMessageAsync(scheduledMessageId, cancellationToken);
        if (cancelled && newScheduledTime.HasValue)
        {
            return await ScheduleMessageAsync(newPayload, newScheduledTime.Value, cancellationToken);
        }

        return new EmailScheduleResult
        {
            ScheduledMessageId = scheduledMessageId,
            IsScheduled = false,
            ErrorMessage = "Failed to update scheduled message",
            ErrorCode = "UPDATE_FAILED",
            Status = "failed",
            Provider = ProviderName
        };
    }

    private Dictionary<string, object> ConvertToSendGridPayload(EmailPayload payload)
    {
        var personalizations = new Dictionary<string, object>
        {
            ["to"] = new[] { new { email = payload.To } },
            ["subject"] = payload.Subject
        };

        // Add CC if provided
        if (payload.Cc?.Any() == true)
        {
            personalizations["cc"] = payload.Cc.Select(email => new { email }).ToArray();
        }

        // Add BCC if provided
        if (payload.Bcc?.Any() == true)
        {
            personalizations["bcc"] = payload.Bcc.Select(email => new { email }).ToArray();
        }

        var sendGridPayload = new Dictionary<string, object>
        {
            ["personalizations"] = new[] { personalizations },
            ["from"] = new { email = payload.From ?? "<EMAIL>" },
            ["content"] = new[]
            {
                new {
                    type = payload.IsHtml ? "text/html" : "text/plain",
                    value = payload.Body
                }
            }
        };

        // Add template data if using templates
        if (!string.IsNullOrEmpty(payload.TemplateId))
        {
            sendGridPayload["template_id"] = payload.TemplateId;
            if (payload.TemplateData?.Any() == true)
            {
                ((Dictionary<string, object>)((object[])sendGridPayload["personalizations"])[0])["dynamic_template_data"] = payload.TemplateData;
            }
        }

        // Add custom headers
        if (payload.Headers?.Any() == true)
        {
            sendGridPayload["headers"] = payload.Headers;
        }

        return sendGridPayload;
    }

    private Dictionary<string, object> ConvertToBulkSendGridPayload(List<EmailPayload> payloads)
    {
        var firstPayload = payloads.First();

        var sendGridPayload = new Dictionary<string, object>
        {
            ["personalizations"] = payloads.Select(p => new Dictionary<string, object>
            {
                ["to"] = new[] { new { email = p.To } },
                ["subject"] = p.Subject
            }).ToArray(),
            ["from"] = new { email = firstPayload.From ?? "<EMAIL>" },
            ["content"] = new[]
            {
                new {
                    type = firstPayload.IsHtml ? "text/html" : "text/plain",
                    value = firstPayload.Body
                }
            }
        };

        return sendGridPayload;
    }

    private string ConvertSendGridStatus(string sendGridStatus)
    {
        return sendGridStatus.ToLower() switch
        {
            "processed" => "sent",
            "delivered" => "delivered",
            "opened" => "read",
            "clicked" => "clicked",
            "bounce" => "failed",
            "dropped" => "failed",
            "deferred" => "deferred",
            "blocked" => "failed",
            "spam_report" => "failed",
            "unsubscribe" => "unsubscribed",
            _ => "unknown"
        };
    }

    private class SendGridErrorResponse
    {
        public SendGridError[]? errors { get; set; }
    }

    private class SendGridError
    {
        public string? message { get; set; }
        public string? field { get; set; }
        public string? help { get; set; }
    }

    private class SendGridMessageStatus
    {
        public string? msg_id { get; set; }
        public string? status { get; set; }
        public DateTime? last_event_time { get; set; }
        public SendGridEvent[]? events { get; set; }
    }

    private class SendGridEvent
    {
        public string? event_name { get; set; }
        public DateTime? processed { get; set; }
        public string? reason { get; set; }
    }

    // ===== ADMIN GATEWAY IMPLEMENTATION =====

    public async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "SendGrid configuration retrieved successfully",
            Configuration = new Dictionary<string, string>
            {
                ["ApiKey"] = _apiKey.Length > 10 ? $"{_apiKey[..10]}..." : "***",
                ["IsEnabled"] = _isInitialized.ToString()
            },
            RequiredFields = new[] { "ApiKey" },
            OptionalFields = Array.Empty<string>()
        };
    }

    public async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            await InitializeAsync(configuration, cancellationToken);

            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = "SendGrid configuration updated successfully",
                Configuration = configuration,
                UpdatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "UPDATE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var templates = new[]
        {
            new GatewayTemplate
            {
                Id = "sendgrid-plain",
                Name = "Plain Text Email",
                Description = "Simple text-only email",
                Content = "{{subject}}: {{text_content}}",
                Variables = new Dictionary<string, string> { ["subject"] = "string", ["text_content"] = "string" }
            },
            new GatewayTemplate
            {
                Id = "sendgrid-html",
                Name = "HTML Email",
                Description = "Rich HTML email with styling",
                Content = "<h1>{{subject}}</h1><p>{{html_content}}</p>",
                Variables = new Dictionary<string, string> { ["subject"] = "string", ["html_content"] = "html" }
            },
            new GatewayTemplate
            {
                Id = "sendgrid-transactional",
                Name = "Transactional Email",
                Description = "Automated transactional email template",
                Content = "{{subject}}: {{message}} [Attachment: {{attachment}}]",
                Variables = new Dictionary<string, string> { ["subject"] = "string", ["message"] = "string", ["attachment"] = "file" }
            }
        };

        return new GatewayTemplatesResult
        {
            IsSuccess = true,
            Templates = templates,
            TotalCount = templates.Length
        };
    }

    public async Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"SendGrid template '{template.Name}' saved successfully",
            Template = template
        };
    }

    public async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"SendGrid template '{templateId}' deleted successfully"
        };
    }

    public async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRetryConfiguration
        {
            IsEnabled = true,
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 2, 8, 32 },
            RetryOnErrors = new[] { "RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 900
        };
    }

    public async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "SendGrid retry configuration updated successfully",
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            var testEmail = new EmailPayload
            {
                ToEmail = "<EMAIL>",
                ToName = "Test Recipient",
                FromEmail = "<EMAIL>",
                FromName = "Test Sender",
                Subject = "SendGrid Gateway Test",
                TextContent = "This is a test email from SendGrid gateway",
                HtmlContent = "<p>This is a test email from SendGrid gateway</p>"
            };

            var testResult = await SendAsync(testEmail, cancellationToken);

            return new GatewayTestResult
            {
                IsSuccess = testResult.IsSuccess,
                Message = testResult.IsSuccess ? "SendGrid gateway test successful" : "SendGrid gateway test failed",
                ResponseTime = DateTime.UtcNow - startTime,
                TestMessageId = testResult.MessageId,
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "LiveTest",
                    ["ToEmail"] = "<EMAIL>"
                },
                ErrorMessage = testResult.ErrorMessage,
                ErrorCode = testResult.ErrorCode
            };
        }
        catch (Exception ex)
        {
            return new GatewayTestResult
            {
                IsSuccess = false,
                Message = "SendGrid gateway test failed with exception",
                ResponseTime = DateTime.UtcNow - startTime,
                ErrorMessage = ex.Message,
                ErrorCode = "TEST_EXCEPTION"
            };
        }
    }

    // ===== METRICS GATEWAY IMPLEMENTATION =====

    public async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var days = (endDate - startDate).Days + 1;
        var totalMessages = Random.Shared.Next(1000, 10000) * days;

        return new GatewayUsageMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalMessages = totalMessages,
            TotalBulkOperations = totalMessages / 5,
            TotalScheduledMessages = totalMessages / 25,
            MessagesByDay = GenerateDailyMetrics(startDate, endDate, totalMessages),
            MessagesByHour = GenerateHourlyMetrics(24, totalMessages),
            MessagesByType = new Dictionary<string, long>
            {
                ["transactional"] = totalMessages * 70 / 100,
                ["marketing"] = totalMessages * 20 / 100,
                ["notification"] = totalMessages * 10 / 100
            },
            AverageMessagesPerDay = totalMessages / (double)days,
            PeakMessagesPerHour = totalMessages / days / 24 * 2.8
        };
    }

    public async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalAttempts = Random.Shared.Next(1000, 10000);
        var successfulDeliveries = (long)(totalAttempts * 0.98);

        return new GatewaySuccessRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalAttempts = totalAttempts,
            SuccessfulDeliveries = successfulDeliveries,
            SuccessRate = (double)successfulDeliveries / totalAttempts * 100,
            SuccessRateByDay = GenerateDailySuccessRate(startDate, endDate, 98.0),
            SuccessRateByHour = GenerateHourlySuccessRate(24, 98.0),
            SuccessfulByType = new Dictionary<string, long>
            {
                ["transactional"] = successfulDeliveries * 70 / 100,
                ["marketing"] = successfulDeliveries * 20 / 100,
                ["notification"] = successfulDeliveries * 10 / 100
            },
            TrendDirection = 0.1
        };
    }

    public async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalFailures = Random.Shared.Next(20, 200);

        return new GatewayFailureRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalFailures = totalFailures,
            FailureRate = 2.0,
            FailuresByErrorCode = new Dictionary<string, long>
            {
                ["BOUNCE"] = totalFailures * 40 / 100,
                ["INVALID_EMAIL"] = totalFailures * 25 / 100,
                ["SPAM_REPORT"] = totalFailures * 15 / 100,
                ["BLOCKED"] = totalFailures * 10 / 100,
                ["DROPPED"] = totalFailures * 10 / 100
            },
            FailuresByDay = GenerateDailyFailures(startDate, endDate, totalFailures),
            TopErrorMessages = new Dictionary<string, string>
            {
                ["BOUNCE"] = "Email bounced - recipient address invalid or mailbox full",
                ["INVALID_EMAIL"] = "Email address format is invalid",
                ["SPAM_REPORT"] = "Recipient marked email as spam"
            },
            MostCommonErrors = new[] { "BOUNCE", "INVALID_EMAIL", "SPAM_REPORT" },
            FailureRateByType = new Dictionary<string, double>
            {
                ["transactional"] = 1.5,
                ["marketing"] = 3.0,
                ["notification"] = 2.2
            }
        };
    }

    public async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayLatencyMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            AverageLatencyMs = 85.2,
            MedianLatencyMs = 70.0,
            P95LatencyMs = 180.0,
            P99LatencyMs = 320.0,
            MinLatencyMs = 15.0,
            MaxLatencyMs = 500.0,
            LatencyByDay = GenerateDailyLatency(startDate, endDate, 85.0),
            LatencyByHour = GenerateHourlyLatency(24, 85.0),
            LatencyByType = new Dictionary<string, double>
            {
                ["transactional"] = 80.0,
                ["marketing"] = 95.0,
                ["notification"] = 75.0
            }
        };
    }

    public async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalSent = Random.Shared.Next(1000, 10000);
        var totalDelivered = (long)(totalSent * 0.98);
        var totalRead = (long)(totalDelivered * 0.35);

        return new GatewayDeliveryCountMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalSent = totalSent,
            TotalDelivered = totalDelivered,
            TotalRead = totalRead,
            TotalBounced = totalSent - totalDelivered,
            TotalComplained = (long)(totalDelivered * 0.001),
            DeliveryByDay = GenerateDailyMetrics(startDate, endDate, totalDelivered),
            ReadByDay = GenerateDailyMetrics(startDate, endDate, totalRead),
            BounceByDay = GenerateDailyMetrics(startDate, endDate, totalSent - totalDelivered),
            DeliveryRate = (double)totalDelivered / totalSent * 100,
            ReadRate = (double)totalRead / totalDelivered * 100,
            BounceRate = (double)(totalSent - totalDelivered) / totalSent * 100
        };
    }

    public async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var usage = await GetUsageMetricsAsync(startDate, endDate, cancellationToken);
        var successRate = await GetSuccessRateMetricsAsync(startDate, endDate, cancellationToken);
        var failureRate = await GetFailureRateMetricsAsync(startDate, endDate, cancellationToken);
        var latency = await GetLatencyMetricsAsync(startDate, endDate, cancellationToken);
        var deliveryCount = await GetDeliveryCountMetricsAsync(startDate, endDate, cancellationToken);

        return new GatewayAnalyticsDashboard
        {
            StartDate = startDate,
            EndDate = endDate,
            Usage = usage,
            SuccessRate = successRate,
            FailureRate = failureRate,
            Latency = latency,
            DeliveryCount = deliveryCount,
            CustomMetrics = new Dictionary<string, object>
            {
                ["SendGridReputation"] = "Excellent",
                ["DomainAuthentication"] = "Verified",
                ["OpenRate"] = "35.2%",
                ["ClickRate"] = "8.7%",
                ["UnsubscribeRate"] = "0.3%"
            },
            Insights = new[]
            {
                "Transactional emails have the highest delivery rates",
                "Marketing emails show higher engagement during weekdays",
                "Bounce rates are lowest for verified domains"
            },
            Recommendations = new[]
            {
                "Implement email list hygiene to reduce bounce rates",
                "Use A/B testing for subject lines to improve open rates",
                "Set up domain authentication for better deliverability"
            }
        };
    }

    public async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRealTimeMetrics
        {
            Timestamp = DateTime.UtcNow,
            MessagesInLast5Minutes = Random.Shared.Next(5, 100),
            MessagesInLastHour = Random.Shared.Next(100, 1000),
            MessagesInLastDay = Random.Shared.Next(1000, 10000),
            CurrentSuccessRate = 98.1,
            CurrentLatencyMs = 82.0,
            ActiveConnections = 3,
            QueuedMessages = Random.Shared.Next(0, 25),
            HealthStatus = "healthy",
            LiveStats = new Dictionary<string, object>
            {
                ["SendGridAPIStatus"] = "operational",
                ["LastSuccessfulSend"] = DateTime.UtcNow.AddMinutes(-1),
                ["DomainReputation"] = "excellent"
            },
            ActiveAlerts = Array.Empty<string>()
        };
    }

    // Helper methods for generating sample metrics data
    private Dictionary<string, long> GenerateDailyMetrics(DateTime startDate, DateTime endDate, long total)
    {
        var result = new Dictionary<string, long>();
        var days = (endDate - startDate).Days + 1;
        var avgPerDay = total / days;

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.Next(-25, 26);
            var dailyCount = Math.Max(0, avgPerDay + (avgPerDay * variance / 100));
            result[date.ToString("yyyy-MM-dd")] = dailyCount;
        }

        return result;
    }

    private Dictionary<string, long> GenerateHourlyMetrics(int hours, long total)
    {
        var result = new Dictionary<string, long>();
        var avgPerHour = total / hours;

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.Next(-35, 36);
            var hourlyCount = Math.Max(0, avgPerHour + (avgPerHour * variance / 100));
            result[hour.ToString("D2")] = hourlyCount;
        }

        return result;
    }

    private Dictionary<string, double> GenerateDailySuccessRate(DateTime startDate, DateTime endDate, double baseRate)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 3 - 1.5;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(95, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlySuccessRate(int hours, double baseRate)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 3 - 1.5;
            result[hour.ToString("D2")] = Math.Max(95, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, long> GenerateDailyFailures(DateTime startDate, DateTime endDate, long totalFailures)
    {
        return GenerateDailyMetrics(startDate, endDate, totalFailures);
    }

    private Dictionary<string, double> GenerateDailyLatency(DateTime startDate, DateTime endDate, double baseLatency)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 40 - 20;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(30, baseLatency + variance);
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlyLatency(int hours, double baseLatency)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 40 - 20;
            result[hour.ToString("D2")] = Math.Max(30, baseLatency + variance);
        }

        return result;
    }
}
