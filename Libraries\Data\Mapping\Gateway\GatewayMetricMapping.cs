using Data.Mapping;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.DbEntities.Gateway;

namespace Data.Mapping.Gateway;

public class GatewayMetricMapping : MappingEntityTypeConfiguration<GatewayMetric>
{
    public override void Configure(EntityTypeBuilder<GatewayMetric> builder)
    {
        builder.ToTable("GatewayMetrics");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.GatewayName)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Provider)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.MetricType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Period)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.PeriodStart)
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.PeriodEnd)
            .HasColumnType("timestamp with time zone");

        builder.Property(x => x.TotalMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.SuccessfulMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.FailedMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.SuccessRate)
            .HasDefaultValue(0.0);

        builder.Property(x => x.FailureRate)
            .HasDefaultValue(0.0);

        builder.Property(x => x.AverageLatencyMs)
            .HasDefaultValue(0.0);

        builder.Property(x => x.MinLatencyMs)
            .HasDefaultValue(0.0);

        builder.Property(x => x.MaxLatencyMs)
            .HasDefaultValue(0.0);

        builder.Property(x => x.DeliveredMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.ReadMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.BouncedMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.ComplainedMessages)
            .HasDefaultValue(0);

        builder.Property(x => x.MetricData)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.MessagesByType)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.ErrorBreakdown)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.CreatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.UpdatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        // Indexes
        builder.HasIndex(x => new { x.GatewayName, x.Provider, x.MetricType, x.Period, x.PeriodStart })
            .IsUnique()
            .HasDatabaseName("IX_GatewayMetrics_Unique");

        builder.HasIndex(x => x.PeriodStart)
            .HasDatabaseName("IX_GatewayMetrics_PeriodStart");

        builder.HasIndex(x => x.PeriodEnd)
            .HasDatabaseName("IX_GatewayMetrics_PeriodEnd");

        builder.HasIndex(x => x.MetricType)
            .HasDatabaseName("IX_GatewayMetrics_MetricType");
    }
}
