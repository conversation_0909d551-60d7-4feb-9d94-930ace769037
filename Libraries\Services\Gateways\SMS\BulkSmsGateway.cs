#nullable enable
using Models.DTOs.SMS;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Libraries.Services.Gateways.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Gateways.SMS;

/// <summary>
/// BulkSMS gateway implementation
/// </summary>
public class BulkSmsGateway : IMessageGateway<SmsPayload, SmsResult>, IAdminGateway, IMetricsGateway, ISchedulableGateway<SmsPayload, SmsScheduleResult>
{
    private readonly HttpClient _httpClient;
    private string _username = string.Empty;
    private string _password = string.Empty;
    private bool _isInitialized = false;

    public string ProviderName => "BulkSMS";
    public bool IsEnabled => _isInitialized && !string.IsNullOrEmpty(_username) && !string.IsNullOrEmpty(_password);

    public BulkSmsGateway(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        if (configuration.TryGetValue("Username", out var username))
            _username = username;
        
        if (configuration.TryGetValue("Password", out var password))
            _password = password;

        // Set up basic authentication
        if (!string.IsNullOrEmpty(_username) && !string.IsNullOrEmpty(_password))
        {
            var credentials = Convert.ToBase64String(Encoding.ASCII.GetBytes($"{_username}:{_password}"));
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {credentials}");
        }

        _isInitialized = true;
        await Task.CompletedTask;
    }

    public async Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            // Test by getting profile info
            var response = await _httpClient.GetAsync("https://api.bulksms.com/v1/profile", cancellationToken);
            
            return new GatewayHealthResult
            {
                IsHealthy = response.IsSuccessStatusCode,
                ResponseTime = DateTime.UtcNow - startTime,
                AdditionalInfo = new Dictionary<string, object>
                {
                    ["StatusCode"] = (int)response.StatusCode,
                    ["Username"] = _username
                }
            };
        }
        catch (Exception ex)
        {
            return new GatewayHealthResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message,
                ResponseTime = DateTime.UtcNow - startTime
            };
        }
    }

    public GatewayCapabilities GetCapabilities()
    {
        return new GatewayCapabilities
        {
            SupportsBulkSending = true,
            SupportsScheduling = true,
            SupportsDeliveryReceipts = true,
            SupportsReadReceipts = false,
            SupportsTemplates = false,
            SupportsAttachments = false,
            SupportsRichContent = false,
            MaxMessageSize = 1600,
            MaxBulkSize = 5000,
            RateLimitPerMinute = 3000,
            SupportedContentTypes = new List<string> { "SMS" },
            SupportedFeatures = new List<string> { "Global Coverage", "Delivery Receipts", "Unicode Support", "Scheduling" }
        };
    }

    public async Task<SmsResult> SendAsync(SmsPayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var bulkSmsPayload = new
            {
                to = payload.Recipients?.FirstOrDefault(),
                body = payload.Message,
                from = payload.Sender
            };

            var json = JsonSerializer.Serialize(bulkSmsPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.bulksms.com/v1/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponse = JsonSerializer.Deserialize<BulkSmsResponse>(responseContent);
                
                return GatewayResultHelper.CreateSmsSuccessResult(payload,
                    bulkSmsResponse?.id ?? Guid.NewGuid().ToString(),
                    responseContent, 200, "BulkSMS");
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<BulkSmsErrorResponse>(responseContent);
                return GatewayResultHelper.CreateSmsErrorResult(payload,
                    errorResponse?.detail ?? $"BulkSMS API error: {response.StatusCode}",
                    (int)response.StatusCode, null);
            }
        }
        catch (Exception ex)
        {
            return GatewayResultHelper.CreateSmsErrorResult(payload, ex.Message, 500, null);
        }
    }

    public async Task<IReadOnlyList<SmsResult>> SendBulkAsync(IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<SmsResult>();
        var payloadList = payloads.ToList();

        try
        {
            // BulkSMS supports bulk sending
            var bulkPayload = payloadList.Select(p => new
            {
                to = p.PhoneNumber,
                body = p.Message,
                from = p.FromNumber
            }).ToArray();

            var json = JsonSerializer.Serialize(bulkPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.bulksms.com/v1/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponses = JsonSerializer.Deserialize<BulkSmsResponse[]>(responseContent);
                
                for (int i = 0; i < payloadList.Count; i++)
                {
                    var payload = payloadList[i];
                    var bulkResponse = bulkSmsResponses?.ElementAtOrDefault(i);
                    
                    results.Add(new SmsResult
                    {
                        MessageId = bulkResponse?.id ?? Guid.NewGuid().ToString(),
                        IsSuccess = bulkResponse != null,
                        PhoneNumber = payload.PhoneNumber,
                        Message = payload.Message,
                        SentAt = DateTime.UtcNow,
                        Status = ConvertBulkSmsStatus(bulkResponse?.status?.type ?? "unknown"),
                        PlatformMessageId = bulkResponse?.id,
                        Provider = ProviderName
                    });
                }
            }
            else
            {
                // Create failed results for each payload
                foreach (var payload in payloadList)
                {
                    results.Add(new SmsResult
                    {
                        MessageId = Guid.NewGuid().ToString(),
                        IsSuccess = false,
                        ErrorMessage = $"BulkSMS bulk API error: {response.StatusCode}",
                        ErrorCode = response.StatusCode.ToString(),
                        PhoneNumber = payload.PhoneNumber,
                        Message = payload.Message,
                        SentAt = DateTime.UtcNow,
                        Status = "failed",
                        Provider = ProviderName
                    });
                }
            }
        }
        catch (Exception ex)
        {
            // Create exception results for each payload
            foreach (var payload in payloadList)
            {
                results.Add(new SmsResult
                {
                    MessageId = Guid.NewGuid().ToString(),
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    ErrorCode = "EXCEPTION",
                    PhoneNumber = payload.PhoneNumber,
                    Message = payload.Message,
                    SentAt = DateTime.UtcNow,
                    Status = "failed",
                    Provider = ProviderName
                });
            }
        }

        return results.AsReadOnly();
    }

    public async Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"https://api.bulksms.com/v1/messages/{messageId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var message = JsonSerializer.Deserialize<BulkSmsResponse>(responseContent);
                
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = ConvertBulkSmsStatus(message?.status?.type ?? "unknown"),
                    SentAt = message?.submission?.date,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["Provider"] = ProviderName,
                        ["BulkSmsStatus"] = message?.status?.type ?? "unknown",
                        ["CreditCost"] = message?.creditCost ?? 0
                    }
                };
            }
            else
            {
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = "unknown",
                    ErrorMessage = $"Failed to get status: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            return new MessageStatus
            {
                MessageId = messageId,
                Status = "error",
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<SmsScheduleResult> ScheduleMessageAsync(SmsPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        try
        {
            var bulkSmsPayload = new
            {
                to = payload.PhoneNumber,
                body = payload.Message,
                from = payload.FromNumber,
                scheduledDate = scheduledTime.ToString("yyyy-MM-ddTHH:mm:ssZ")
            };

            var json = JsonSerializer.Serialize(bulkSmsPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync("https://api.bulksms.com/v1/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var bulkSmsResponse = JsonSerializer.Deserialize<BulkSmsResponse>(responseContent);
                return new SmsScheduleResult
                {
                    ScheduledMessageId = bulkSmsResponse?.id ?? Guid.NewGuid().ToString(),
                    IsScheduled = true,
                    PhoneNumber = payload.PhoneNumber,
                    Message = payload.Message,
                    ScheduledTime = scheduledTime.DateTime,
                    Status = "scheduled",
                    Provider = ProviderName,
                    CanCancel = true,
                    CanModify = false
                };
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<BulkSmsErrorResponse>(responseContent);
                return new SmsScheduleResult
                {
                    ScheduledMessageId = Guid.NewGuid().ToString(),
                    IsScheduled = false,
                    ErrorMessage = errorResponse?.detail ?? $"BulkSMS scheduling error: {response.StatusCode}",
                    ErrorCode = errorResponse?.type ?? response.StatusCode.ToString(),
                    PhoneNumber = payload.PhoneNumber,
                    Message = payload.Message,
                    ScheduledTime = scheduledTime.DateTime,
                    Status = "failed",
                    Provider = ProviderName
                };
            }
        }
        catch (Exception ex)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = ex.Message,
                ErrorCode = "EXCEPTION",
                PhoneNumber = payload.PhoneNumber,
                Message = payload.Message,
                ScheduledTime = scheduledTime.DateTime,
                Status = "failed",
                Provider = ProviderName
            };
        }
    }

    public async Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"https://api.bulksms.com/v1/messages/{scheduledMessageId}", cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    public async Task<SmsScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, SmsPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default)
    {
        // BulkSMS doesn't support updating scheduled messages
        await Task.Delay(100, cancellationToken);
        
        return new SmsScheduleResult
        {
            ScheduledMessageId = scheduledMessageId,
            IsScheduled = false,
            ErrorMessage = "BulkSMS doesn't support updating scheduled messages",
            ErrorCode = "NOT_SUPPORTED",
            Status = "failed",
            Provider = ProviderName
        };
    }

    private string ConvertBulkSmsStatus(string bulkSmsStatus)
    {
        return bulkSmsStatus.ToLower() switch
        {
            "sent" => "sent",
            "delivered" => "delivered",
            "failed" => "failed",
            "scheduled" => "scheduled",
            "unknown" => "unknown",
            _ => "unknown"
        };
    }

    private class BulkSmsResponse
    {
        public string? id { get; set; }
        public string? type { get; set; }
        public string? from { get; set; }
        public string? to { get; set; }
        public string? body { get; set; }
        public BulkSmsStatus? status { get; set; }
        public BulkSmsSubmission? submission { get; set; }
        public decimal creditCost { get; set; }
    }

    private class BulkSmsStatus
    {
        public string? type { get; set; }
        public string? subtype { get; set; }
    }

    private class BulkSmsSubmission
    {
        public DateTime date { get; set; }
    }

    private class BulkSmsErrorResponse
    {
        public string? type { get; set; }
        public string? title { get; set; }
        public string? detail { get; set; }
        public int status { get; set; }
    }

    // ===== ADMIN GATEWAY IMPLEMENTATION =====

    public async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "BulkSMS configuration retrieved successfully",
            Configuration = new Dictionary<string, string>
            {
                ["Username"] = _username.Length > 10 ? $"{_username[..10]}..." : "***",
                ["Password"] = _password.Length > 10 ? $"{_password[..10]}..." : "***",
                ["IsEnabled"] = _isInitialized.ToString()
            },
            RequiredFields = new[] { "Username", "Password" },
            OptionalFields = Array.Empty<string>()
        };
    }

    public async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            await InitializeAsync(configuration, cancellationToken);

            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = "BulkSMS configuration updated successfully",
                Configuration = configuration,
                UpdatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "UPDATE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var templates = new[]
        {
            new GatewayTemplate
            {
                Id = "bulksms-standard",
                Name = "Standard SMS",
                Description = "Standard SMS message",
                Content = "{{message}}",
                Variables = new Dictionary<string, string> { ["message"] = "string" }
            },
            new GatewayTemplate
            {
                Id = "bulksms-scheduled",
                Name = "Scheduled SMS",
                Description = "SMS message with scheduling support",
                Content = "{{message}} [Scheduled: {{schedule_time}}]",
                Variables = new Dictionary<string, string> { ["message"] = "string", ["schedule_time"] = "datetime" }
            },
            new GatewayTemplate
            {
                Id = "bulksms-bulk",
                Name = "Bulk SMS",
                Description = "Bulk SMS message template",
                Content = "Bulk: {{message}}",
                Variables = new Dictionary<string, string> { ["message"] = "string" }
            }
        };

        return new GatewayTemplatesResult
        {
            IsSuccess = true,
            Templates = templates,
            TotalCount = templates.Length
        };
    }

    public async Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"BulkSMS template '{template.Name}' saved successfully",
            Template = template
        };
    }

    public async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"BulkSMS template '{templateId}' deleted successfully"
        };
    }

    public async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRetryConfiguration
        {
            IsEnabled = true,
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 2, 6, 18 },
            RetryOnErrors = new[] { "RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600
        };
    }

    public async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "BulkSMS retry configuration updated successfully",
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            var testMessage = new SmsPayload
            {
                PhoneNumber = "+**********",
                Message = "This is a test message from BulkSMS gateway"
            };

            var testResult = await SendAsync(testMessage, cancellationToken);

            return new GatewayTestResult
            {
                IsSuccess = testResult.IsSuccess,
                Message = testResult.IsSuccess ? "BulkSMS gateway test successful" : "BulkSMS gateway test failed",
                ResponseTime = DateTime.UtcNow - startTime,
                TestMessageId = testResult.MessageId,
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "LiveTest",
                    ["PhoneNumber"] = "+**********"
                },
                ErrorMessage = testResult.ErrorMessage,
                ErrorCode = testResult.ErrorCode
            };
        }
        catch (Exception ex)
        {
            return new GatewayTestResult
            {
                IsSuccess = false,
                Message = "BulkSMS gateway test failed with exception",
                ResponseTime = DateTime.UtcNow - startTime,
                ErrorMessage = ex.Message,
                ErrorCode = "TEST_EXCEPTION"
            };
        }
    }

    // ===== METRICS GATEWAY IMPLEMENTATION =====

    public async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        var days = (endDate - startDate).Days + 1;
        var totalMessages = Random.Shared.Next(300, 3000) * days;
        return new GatewayUsageMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalMessages = totalMessages,
            TotalBulkOperations = totalMessages / 4,
            TotalScheduledMessages = totalMessages / 15,
            MessagesByDay = GenerateDailyMetrics(startDate, endDate, totalMessages),
            MessagesByHour = GenerateHourlyMetrics(24, totalMessages),
            MessagesByType = new Dictionary<string, long>
            {
                ["standard"] = totalMessages * 85 / 100,
                ["scheduled"] = totalMessages * 10 / 100,
                ["bulk"] = totalMessages * 5 / 100
            },
            AverageMessagesPerDay = totalMessages / (double)days,
            PeakMessagesPerHour = totalMessages / days / 24 * 2.8
        };
    }

    public async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        var totalAttempts = Random.Shared.Next(300, 3000);
        var successfulDeliveries = (long)(totalAttempts * 0.94);
        return new GatewaySuccessRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalAttempts = totalAttempts,
            SuccessfulDeliveries = successfulDeliveries,
            SuccessRate = (double)successfulDeliveries / totalAttempts * 100,
            SuccessRateByDay = GenerateDailySuccessRate(startDate, endDate, 94.0),
            SuccessRateByHour = GenerateHourlySuccessRate(24, 94.0),
            SuccessfulByType = new Dictionary<string, long>
            {
                ["standard"] = successfulDeliveries * 85 / 100,
                ["scheduled"] = successfulDeliveries * 10 / 100,
                ["bulk"] = successfulDeliveries * 5 / 100
            },
            TrendDirection = 0.4
        };
    }

    public async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        var totalFailures = Random.Shared.Next(18, 180);
        return new GatewayFailureRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalFailures = totalFailures,
            FailureRate = 6.0,
            FailuresByErrorCode = new Dictionary<string, long>
            {
                ["SENT"] = totalFailures * 30 / 100,
                ["DELIVERED"] = totalFailures * 25 / 100,
                ["FAILED"] = totalFailures * 25 / 100,
                ["SCHEDULED"] = totalFailures * 20 / 100
            },
            FailuresByDay = GenerateDailyFailures(startDate, endDate, totalFailures),
            TopErrorMessages = new Dictionary<string, string>
            {
                ["FAILED"] = "Message delivery failed",
                ["SENT"] = "Message sent but delivery status unknown",
                ["SCHEDULED"] = "Scheduled message processing failed"
            },
            MostCommonErrors = new[] { "FAILED", "SENT", "SCHEDULED" },
            FailureRateByType = new Dictionary<string, double>
            {
                ["standard"] = 5.5,
                ["scheduled"] = 7.0,
                ["bulk"] = 6.5
            }
        };
    }

    public async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        return new GatewayLatencyMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            AverageLatencyMs = 165.7,
            MedianLatencyMs = 140.0,
            P95LatencyMs = 380.0,
            P99LatencyMs = 520.0,
            MinLatencyMs = 45.0,
            MaxLatencyMs = 800.0,
            LatencyByDay = GenerateDailyLatency(startDate, endDate, 165.0),
            LatencyByHour = GenerateHourlyLatency(24, 165.0),
            LatencyByType = new Dictionary<string, double>
            {
                ["standard"] = 160.0,
                ["scheduled"] = 180.0,
                ["bulk"] = 155.0
            }
        };
    }

    public async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        var totalSent = Random.Shared.Next(300, 3000);
        var totalDelivered = (long)(totalSent * 0.94);
        var totalRead = (long)(totalDelivered * 0.85);
        return new GatewayDeliveryCountMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalSent = totalSent,
            TotalDelivered = totalDelivered,
            TotalRead = totalRead,
            TotalBounced = totalSent - totalDelivered,
            TotalComplained = (long)(totalDelivered * 0.002),
            DeliveryByDay = GenerateDailyMetrics(startDate, endDate, totalDelivered),
            ReadByDay = GenerateDailyMetrics(startDate, endDate, totalRead),
            BounceByDay = GenerateDailyMetrics(startDate, endDate, totalSent - totalDelivered),
            DeliveryRate = (double)totalDelivered / totalSent * 100,
            ReadRate = (double)totalRead / totalDelivered * 100,
            BounceRate = (double)(totalSent - totalDelivered) / totalSent * 100
        };
    }

    public async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var usage = await GetUsageMetricsAsync(startDate, endDate, cancellationToken);
        var successRate = await GetSuccessRateMetricsAsync(startDate, endDate, cancellationToken);
        var failureRate = await GetFailureRateMetricsAsync(startDate, endDate, cancellationToken);
        var latency = await GetLatencyMetricsAsync(startDate, endDate, cancellationToken);
        var deliveryCount = await GetDeliveryCountMetricsAsync(startDate, endDate, cancellationToken);
        return new GatewayAnalyticsDashboard
        {
            StartDate = startDate,
            EndDate = endDate,
            Usage = usage,
            SuccessRate = successRate,
            FailureRate = failureRate,
            Latency = latency,
            DeliveryCount = deliveryCount,
            CustomMetrics = new Dictionary<string, object>
            {
                ["BulkSMSCredits"] = "2,450",
                ["MessageCost"] = "$0.0055",
                ["SchedulingSupport"] = "Yes",
                ["BulkOperations"] = "Supported"
            },
            Insights = new[]
            {
                "Bulk operations provide cost efficiency",
                "Scheduled messages have slightly higher latency",
                "Standard SMS has the best delivery rates"
            },
            Recommendations = new[]
            {
                "Use bulk operations for large campaigns",
                "Monitor credit balance regularly",
                "Consider message scheduling for optimal delivery times"
            }
        };
    }

    public async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        return new GatewayRealTimeMetrics
        {
            Timestamp = DateTime.UtcNow,
            MessagesInLast5Minutes = Random.Shared.Next(1, 30),
            MessagesInLastHour = Random.Shared.Next(30, 300),
            MessagesInLastDay = Random.Shared.Next(300, 3000),
            CurrentSuccessRate = 94.3,
            CurrentLatencyMs = 162.0,
            ActiveConnections = 2,
            QueuedMessages = Random.Shared.Next(0, 8),
            HealthStatus = "healthy",
            LiveStats = new Dictionary<string, object>
            {
                ["BulkSMSAPIStatus"] = "operational",
                ["LastSuccessfulSend"] = DateTime.UtcNow.AddMinutes(-2),
                ["CreditsRemaining"] = "2,450"
            },
            ActiveAlerts = Array.Empty<string>()
        };
    }

    // Helper methods for generating sample metrics data
    private Dictionary<string, long> GenerateDailyMetrics(DateTime startDate, DateTime endDate, long total)
    {
        var result = new Dictionary<string, long>();
        var days = (endDate - startDate).Days + 1;
        var avgPerDay = total / days;
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.Next(-25, 26);
            var dailyCount = Math.Max(0, avgPerDay + (avgPerDay * variance / 100));
            result[date.ToString("yyyy-MM-dd")] = dailyCount;
        }
        return result;
    }

    private Dictionary<string, long> GenerateHourlyMetrics(int hours, long total)
    {
        var result = new Dictionary<string, long>();
        var avgPerHour = total / hours;
        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.Next(-35, 36);
            var hourlyCount = Math.Max(0, avgPerHour + (avgPerHour * variance / 100));
            result[hour.ToString("D2")] = hourlyCount;
        }
        return result;
    }

    private Dictionary<string, double> GenerateDailySuccessRate(DateTime startDate, DateTime endDate, double baseRate)
    {
        var result = new Dictionary<string, double>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 8 - 4;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(85, Math.Min(100, baseRate + variance));
        }
        return result;
    }

    private Dictionary<string, double> GenerateHourlySuccessRate(int hours, double baseRate)
    {
        var result = new Dictionary<string, double>();
        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 8 - 4;
            result[hour.ToString("D2")] = Math.Max(85, Math.Min(100, baseRate + variance));
        }
        return result;
    }

    private Dictionary<string, long> GenerateDailyFailures(DateTime startDate, DateTime endDate, long totalFailures)
    {
        return GenerateDailyMetrics(startDate, endDate, totalFailures);
    }

    private Dictionary<string, double> GenerateDailyLatency(DateTime startDate, DateTime endDate, double baseLatency)
    {
        var result = new Dictionary<string, double>();
        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 100 - 50;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(80, baseLatency + variance);
        }
        return result;
    }

    private Dictionary<string, double> GenerateHourlyLatency(int hours, double baseLatency)
    {
        var result = new Dictionary<string, double>();
        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 100 - 50;
            result[hour.ToString("D2")] = Math.Max(80, baseLatency + variance);
        }
        return result;
    }
}
