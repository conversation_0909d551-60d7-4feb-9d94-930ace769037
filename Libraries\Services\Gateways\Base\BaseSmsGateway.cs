using Models.DTOs.SMS;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Services.Gateway;

namespace Services.Gateways.Base
{
    public abstract class BaseSmsGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName) : BaseGateway(httpClient, dataService, gatewayName), ISmsGateway, IAdminGateway, IMetricsGateway
    {

        // IGateway interface implementations
        public abstract string ProviderName { get; }
        public virtual bool IsEnabled => true;
        public virtual Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        // ISmsGateway interface implementations
        public virtual Task<SmsResult> SendWithTemplateAsync(string templateId, object templateData, string recipient, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Template sending not implemented for this SMS gateway");
        }

        public virtual Task<IReadOnlyList<SmsResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipient)> recipients, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Bulk template sending not implemented for this SMS gateway");
        }

        public virtual Task<PhoneValidationResult> ValidatePhoneAsync(string phoneNumber, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new PhoneValidationResult
            {
                IsValid = !string.IsNullOrWhiteSpace(phoneNumber),
                PhoneNumber = phoneNumber,
                FormattedNumber = phoneNumber,
                CountryCode = null,
                Carrier = null,
                NumberType = null
            });
        }

        // Abstract methods that must be implemented by derived classes
        public abstract Task<SmsResult> SendAsync(SmsPayload payload, CancellationToken cancellationToken = default);
        public abstract Task<IReadOnlyList<SmsResult>> SendBulkAsync(IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken = default);
        public abstract Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
        public abstract Task<SmsScheduleResult> ScheduleMessageAsync(SmsPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
        public abstract Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
        public abstract Task<SmsScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, SmsPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
        public abstract Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);
        public abstract GatewayCapabilities GetCapabilities();

        // Helper methods for creating standardized results
        protected static SmsResult CreateSuccessResult(string messageId, string? responseContent = null)
        {
            return new SmsResult
            {
                MessageId = messageId,
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        protected static SmsResult CreateErrorResult(string errorMessage, string? messageId = null)
        {
            return new SmsResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        protected static SmsScheduleResult CreateSuccessScheduleResult(string scheduledMessageId, DateTimeOffset scheduledTime, string? responseContent = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId,
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        protected static SmsScheduleResult CreateErrorScheduleResult(DateTimeOffset scheduledTime, string errorMessage, string? scheduledMessageId = null)
        {
            return new SmsScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // Default implementations for IAdminGateway
        public virtual async Task<GatewayTestResult> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var testMessage = CreateTestSmsPayload(_gatewayName);
                
                var testResult = await SendAsync(testMessage, cancellationToken);
                return CreateTestResult(
                    testResult.IsSuccess,
                    testResult.IsSuccess ? $"{_gatewayName} gateway test successful" : $"{_gatewayName} gateway test failed",
                    DateTime.UtcNow - startTime,
                    testResult.MessageId,
                    testResult.ErrorMessage
                );
            }
            catch (Exception ex)
            {
                return CreateTestResult(
                    false,
                    $"{_gatewayName} gateway test failed",
                    DateTime.UtcNow - startTime,
                    null,
                    ex.Message
                );
            }
        }

        public virtual async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> config, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveGatewayConfigurationAsync(_gatewayName, "SMS", ProviderName, config);
                var configDict = config?.ToDictionary(kvp => kvp.Key, kvp => (object)kvp.Value) ?? new Dictionary<string, object>();
                return CreateSuccessConfigurationResult(configDict, _gatewayName);
            }
            catch (Exception ex)
            {
                return CreateErrorConfigurationResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
        {
            var config = await _dataService.GetGatewayConfigurationAsync(_gatewayName, ProviderName);
            var configDict = config?.ConfigurationData != null
                ? System.Text.Json.JsonSerializer.Deserialize<Dictionary<string, object>>(config.ConfigurationData) ?? new Dictionary<string, object>()
                : new Dictionary<string, object>();
            return CreateSuccessConfigurationResult(configDict, _gatewayName);
        }

        public virtual Task<GatewayTemplateResult> CreateTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Template creation not implemented for this SMS gateway");
        }

        public virtual Task<GatewayTemplateResult> UpdateTemplateAsync(string templateId, GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Template update not implemented for this SMS gateway");
        }

        public virtual async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
        {
            try
            {
                var result = await _dataService.DeleteTemplateResultAsync(_gatewayName, ProviderName, templateId);
                return result;
            }
            catch (Exception ex)
            {
                return CreateErrorTemplateResult(ex.Message, _gatewayName);
            }
        }

        public virtual async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
        {
            var templates = await _dataService.GetTemplatesResultAsync(_gatewayName, ProviderName);
            return templates;
        }

        public virtual Task<GatewayRetryPolicyResult> SetRetryPolicyAsync(GatewayRetryPolicy policy, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new GatewayRetryPolicyResult
            {
                IsSuccess = true,
                RetryPolicy = policy,
                GatewayName = _gatewayName
            });
        }

        public virtual Task<GatewayRetryPolicy> GetRetryPolicyAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new GatewayRetryPolicy());
        }

        // Default implementations for IMetricsGateway
        public virtual Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Usage metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewayPerformanceMetrics> GetPerformanceMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Performance metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Success rate metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Failure rate metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Latency metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Delivery count metrics not implemented for this SMS gateway");
        }

        public virtual Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Analytics dashboard not implemented for this SMS gateway");
        }

        public virtual Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Real-time metrics not implemented for this SMS gateway");
        }

        public virtual async Task LogMessageAsync(string messageId, string status, Dictionary<string, object>? metadata = null, CancellationToken cancellationToken = default)
        {
            await _dataService.LogMessageAsync(messageId, _gatewayName, ProviderName, "SMS", status, null, null, null, null, null, null, null, null, 0, metadata);
        }

        public virtual Task RecordMetricAsync(string metricName, double value, Dictionary<string, string>? tags = null, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Metric recording not implemented for this SMS gateway");
        }

        // Missing IAdminGateway methods
        public virtual async Task<GatewayTemplateResult> SaveTemplateAsync(Models.DTOs.Gateway.GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                var savedTemplate = await _dataService.SaveTemplateResultAsync(_gatewayName, ProviderName, template);
                return savedTemplate;
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new GatewayRetryConfiguration());
        }

        public virtual Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration configuration, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new GatewayConfigurationResult
            {
                IsSuccess = true,
                GatewayName = _gatewayName
            });
        }

        public virtual Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
        {
            return Task.FromResult(new GatewayTestResult
            {
                IsSuccess = true,
                Message = $"{_gatewayName} gateway test completed successfully",
                ResponseTime = TimeSpan.FromMilliseconds(100),
                TestMessageId = Guid.NewGuid().ToString(),
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "Connection",
                    ["GatewayName"] = _gatewayName,
                    ["Timestamp"] = DateTime.UtcNow
                }
            });
        }
    }
}
