#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Push;
using Services.Interfaces;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Push notification administration endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize(Policy = "OnlyAdmins")]
[Produces("application/json")]
public class PushAdminController : ControllerBase
{
    private readonly IMessageSender<PushPayload, PushResult> _pushSender;

    public PushAdminController(IMessageSender<PushPayload, PushResult> pushSender)
    {
        _pushSender = pushSender;
    }

    /// <summary>
    /// Get push notification service configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Current configuration</returns>
    /// <response code="200">Configuration retrieved successfully</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpGet("config")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> Config(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Provider = "Firebase Cloud Messaging",
            ServerKey = "****" + "1234", // Masked for security
            SenderId = "123456789",
            ProjectId = "my-push-project",
            IsEnabled = true,
            MaxRetries = 3,
            TimeoutSeconds = 30,
            BatchSize = 500,
            RateLimitPerMinute = 1000,
            LastUpdated = DateTime.UtcNow.AddDays(-2),
            Status = "Active"
        });
    }

    /// <summary>
    /// Update push notification service configuration
    /// </summary>
    /// <param name="request">Configuration update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update result</returns>
    /// <response code="200">Configuration updated successfully</response>
    /// <response code="400">Invalid configuration</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpPut("config")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> UpdateConfig([FromBody] PushConfigurationRequest request, CancellationToken cancellationToken)
    {
        // Validate configuration
        if (string.IsNullOrWhiteSpace(request.ServerKey))
        {
            return BadRequest(new { Error = "Server key is required" });
        }

        return await Task.FromResult(new
        {
            Success = true,
            Message = "Push configuration updated successfully",
            UpdatedAt = DateTime.UtcNow,
            Configuration = new
            {
                Provider = request.Provider,
                ServerKey = "****" + request.ServerKey.Substring(Math.Max(0, request.ServerKey.Length - 4)),
                SenderId = request.SenderId,
                ProjectId = request.ProjectId,
                IsEnabled = request.IsEnabled,
                MaxRetries = request.MaxRetries,
                TimeoutSeconds = request.TimeoutSeconds
            }
        });
    }

    /// <summary>
    /// Get authentication configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Authentication details</returns>
    /// <response code="200">Authentication details retrieved</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpGet("auth")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> Auth(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            AuthType = "Server Key",
            ServerKey = "****" + "1234", // Masked
            ServiceAccountKey = "****" + "service.json", // Masked
            IsValid = true,
            ExpiresAt = DateTime.UtcNow.AddYears(1),
            LastValidated = DateTime.UtcNow.AddHours(-1),
            Scopes = new[] { "https://www.googleapis.com/auth/firebase.messaging" }
        });
    }

    /// <summary>
    /// Update authentication configuration
    /// </summary>
    /// <param name="request">Authentication update request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Update result</returns>
    /// <response code="200">Authentication updated successfully</response>
    /// <response code="400">Invalid authentication data</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpPut("auth")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> UpdateAuth([FromBody] PushAuthRequest request, CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Success = true,
            Message = "Push authentication updated successfully",
            UpdatedAt = DateTime.UtcNow,
            AuthType = request.AuthType,
            IsValid = true
        });
    }

    /// <summary>
    /// Select and configure push notification provider with enhanced validation and testing
    /// </summary>
    /// <param name="request">Provider selection and configuration request</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Provider configuration result with validation details</returns>
    /// <response code="200">Provider configured successfully</response>
    /// <response code="400">Invalid provider or configuration</response>
    /// <response code="401">Unauthorized - authentication required</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpPost("provider")]
    [ProducesResponseType(typeof(ProviderConfigurationResult), 200)]
    [ProducesResponseType(typeof(ErrorResponse), 400)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<IActionResult> Provider([FromBody] ProviderRequest request, CancellationToken cancellationToken)
    {
        // Enhanced provider definitions with capabilities
        var availableProviders = new Dictionary<string, ProviderInfo>
        {
            ["FCM"] = new ProviderInfo
            {
                Name = "Firebase Cloud Messaging (FCM)",
                RequiredFields = new[] { "ServerKey", "SenderId" },
                OptionalFields = new[] { "ProjectId" },
                SupportedFeatures = new[] { "Android", "iOS", "Web", "Data Payloads", "Topic Messaging" },
                MaxPayloadSize = 4096,
                RateLimit = "600 requests/minute",
                Endpoints = new Dictionary<string, string>
                {
                    ["Legacy"] = "https://fcm.googleapis.com/fcm/send",
                    ["V1"] = "https://fcm.googleapis.com/v1/projects/{project-id}/messages:send"
                }
            },
            ["OneSignal"] = new ProviderInfo
            {
                Name = "OneSignal",
                RequiredFields = new[] { "AppId", "RestApiKey" },
                OptionalFields = new[] { "UserAuthKey" },
                SupportedFeatures = new[] { "Android", "iOS", "Web", "Rich Media", "A/B Testing", "Segments" },
                MaxPayloadSize = 2048,
                RateLimit = "2000 requests/minute",
                Endpoints = new Dictionary<string, string>
                {
                    ["Notifications"] = "https://onesignal.com/api/v1/notifications",
                    ["Players"] = "https://onesignal.com/api/v1/players"
                }
            }
        };

        // Normalize provider name for lookup
        var normalizedProvider = request.Provider?.Replace("Firebase Cloud Messaging (", "").Replace(")", "").Trim();
        if (normalizedProvider == "FCM" || request.Provider == "Firebase Cloud Messaging (FCM)")
        {
            normalizedProvider = "FCM";
        }

        if (!availableProviders.ContainsKey(normalizedProvider))
        {
            return BadRequest(new ErrorResponse
            {
                Error = "Invalid provider",
                Details = "Provider not supported",
                AvailableProviders = availableProviders.Values.Select(p => p.Name).ToArray(),
                Timestamp = DateTime.UtcNow,
                SuggestedActions = new[] { "Choose from available providers", "Check provider name spelling" }
            });
        }

        var providerInfo = availableProviders[normalizedProvider];

        // Enhanced validation with detailed feedback
        var validationResult = ValidateProviderConfiguration(normalizedProvider, request.Configuration, providerInfo);
        if (!validationResult.IsValid)
        {
            return BadRequest(new ErrorResponse
            {
                Error = "Invalid configuration",
                Details = "Configuration validation failed",
                ValidationErrors = validationResult.Errors,
                RequiredFields = providerInfo.RequiredFields,
                OptionalFields = providerInfo.OptionalFields,
                Timestamp = DateTime.UtcNow,
                SuggestedActions = new[] { "Provide all required fields", "Check field names and values" }
            });
        }

        try
        {
            // Test provider connectivity before storing configuration
            var connectivityTest = await TestProviderConnectivity(normalizedProvider, request.Configuration, cancellationToken);

            // Store provider configuration (implement your storage logic)
            await StoreProviderConfiguration(normalizedProvider, request.Configuration, cancellationToken);

            return Ok(new ProviderConfigurationResult
            {
                Success = true,
                Message = $"Push provider configured successfully: {providerInfo.Name}",
                Provider = providerInfo.Name,
                ConfiguredAt = DateTime.UtcNow,
                Status = "active",
                ProviderInfo = providerInfo,
                ConnectivityTest = connectivityTest,
                NextSteps = new[]
                {
                    "Use /push/send endpoint to send notifications",
                    "Monitor delivery status via /push/metrics endpoint",
                    "Configure templates via /push-admin/templates endpoint",
                    "Test with /push-admin/test endpoint"
                }
            });
        }
        catch (Exception ex)
        {
            return StatusCode(500, new ErrorResponse
            {
                Error = "Failed to configure provider",
                Details = ex.Message,
                Timestamp = DateTime.UtcNow,
                SuggestedActions = new[]
                {
                    "Verify API credentials are correct",
                    "Check network connectivity",
                    "Review provider documentation",
                    "Contact support if issue persists"
                }
            });
        }
    }

    /// <summary>
    /// Get available push notification templates
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Available templates</returns>
    /// <response code="200">Templates retrieved successfully</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpGet("templates")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> Templates(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Templates = new[]
            {
                new { Id = "welcome", Name = "Welcome Notification", Description = "Welcome new users" },
                new { Id = "order-update", Name = "Order Update", Description = "Order status updates" },
                new { Id = "promotion", Name = "Promotional", Description = "Marketing promotions" },
                new { Id = "reminder", Name = "Reminder", Description = "General reminders" }
            },
            TotalCount = 4
        });
    }

    /// <summary>
    /// Get retry configuration
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Retry settings</returns>
    /// <response code="200">Retry configuration retrieved</response>
    /// <response code="401">Unauthorized</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [HttpGet("retries")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> Retries(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 1, 5, 15 },
            RetryOnErrors = new[] { "UNAVAILABLE", "INTERNAL", "TIMEOUT" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600,
            IsEnabled = true
        });
    }

    // Helper methods for enhanced provider functionality
    private ValidationResult ValidateProviderConfiguration(string provider, Dictionary<string, string>? configuration, ProviderInfo providerInfo)
    {
        var result = new ValidationResult { IsValid = true, Errors = new List<string>() };

        if (configuration == null)
        {
            result.IsValid = false;
            result.Errors.Add("Configuration is required");
            return result;
        }

        // Check required fields
        foreach (var requiredField in providerInfo.RequiredFields)
        {
            if (!configuration.ContainsKey(requiredField) || string.IsNullOrWhiteSpace(configuration[requiredField]))
            {
                result.IsValid = false;
                result.Errors.Add($"Required field '{requiredField}' is missing or empty");
            }
        }

        // Provider-specific validation
        switch (provider)
        {
            case "FCM":
                if (configuration.ContainsKey("ServerKey") && !configuration["ServerKey"].StartsWith("AAAA"))
                {
                    result.Errors.Add("FCM Server Key should start with 'AAAA'");
                    result.IsValid = false;
                }
                break;
            case "OneSignal":
                if (configuration.ContainsKey("AppId") && !Guid.TryParse(configuration["AppId"], out _))
                {
                    result.Errors.Add("OneSignal App ID should be a valid GUID");
                    result.IsValid = false;
                }
                break;
        }

        return result;
    }

    private async Task<ConnectivityTestResult> TestProviderConnectivity(string provider, Dictionary<string, string> configuration, CancellationToken cancellationToken)
    {
        try
        {
            using var httpClient = new HttpClient();
            httpClient.Timeout = TimeSpan.FromSeconds(10);

            switch (provider)
            {
                case "FCM":
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"key={configuration["ServerKey"]}");
                    var fcmResponse = await httpClient.PostAsync("https://fcm.googleapis.com/fcm/send",
                        new StringContent("{\"registration_ids\":[]}", Encoding.UTF8, "application/json"), cancellationToken);

                    return new ConnectivityTestResult
                    {
                        IsSuccessful = fcmResponse.StatusCode != System.Net.HttpStatusCode.Unauthorized,
                        ResponseTime = TimeSpan.FromMilliseconds(100),
                        StatusCode = (int)fcmResponse.StatusCode,
                        Message = fcmResponse.IsSuccessStatusCode ? "Connection successful" : "Authentication failed"
                    };

                case "OneSignal":
                    httpClient.DefaultRequestHeaders.Add("Authorization", $"Basic {configuration["RestApiKey"]}");
                    var oneSignalResponse = await httpClient.GetAsync($"https://onesignal.com/api/v1/apps/{configuration["AppId"]}", cancellationToken);

                    return new ConnectivityTestResult
                    {
                        IsSuccessful = oneSignalResponse.IsSuccessStatusCode,
                        ResponseTime = TimeSpan.FromMilliseconds(150),
                        StatusCode = (int)oneSignalResponse.StatusCode,
                        Message = oneSignalResponse.IsSuccessStatusCode ? "Connection successful" : "Authentication failed"
                    };

                default:
                    return new ConnectivityTestResult
                    {
                        IsSuccessful = false,
                        Message = "Provider not supported for connectivity testing"
                    };
            }
        }
        catch (Exception ex)
        {
            return new ConnectivityTestResult
            {
                IsSuccessful = false,
                Message = $"Connectivity test failed: {ex.Message}"
            };
        }
    }

    private async Task StoreProviderConfiguration(string provider, Dictionary<string, string> configuration, CancellationToken cancellationToken)
    {
        // Implement your storage logic here (database, configuration service, etc.)
        await Task.Delay(100, cancellationToken);
        // For now, just simulate storage
    }
}

/// <summary>
/// Enhanced provider information model
/// </summary>
public class ProviderInfo
{
    public string Name { get; set; } = string.Empty;
    public string[] RequiredFields { get; set; } = Array.Empty<string>();
    public string[] OptionalFields { get; set; } = Array.Empty<string>();
    public string[] SupportedFeatures { get; set; } = Array.Empty<string>();
    public int MaxPayloadSize { get; set; }
    public string RateLimit { get; set; } = string.Empty;
    public Dictionary<string, string> Endpoints { get; set; } = new();
}

/// <summary>
/// Provider configuration result model
/// </summary>
public class ProviderConfigurationResult
{
    public bool Success { get; set; }
    public string Message { get; set; } = string.Empty;
    public string Provider { get; set; } = string.Empty;
    public DateTime ConfiguredAt { get; set; }
    public string Status { get; set; } = string.Empty;
    public ProviderInfo? ProviderInfo { get; set; }
    public ConnectivityTestResult? ConnectivityTest { get; set; }
    public string[] NextSteps { get; set; } = Array.Empty<string>();
}

/// <summary>
/// Connectivity test result model
/// </summary>
public class ConnectivityTestResult
{
    public bool IsSuccessful { get; set; }
    public TimeSpan ResponseTime { get; set; }
    public int StatusCode { get; set; }
    public string Message { get; set; } = string.Empty;
}

/// <summary>
/// Validation result model
/// </summary>
public class ValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
}

/// <summary>
/// Error response model
/// </summary>
public class ErrorResponse
{
    public string Error { get; set; } = string.Empty;
    public string Details { get; set; } = string.Empty;
    public string[]? AvailableProviders { get; set; }
    public string[]? ValidationErrors { get; set; }
    public string[]? RequiredFields { get; set; }
    public string[]? OptionalFields { get; set; }
    public string[]? SuggestedActions { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// Push configuration request model
/// </summary>
public class PushConfigurationRequest
{
    public string Provider { get; set; } = string.Empty;
    public string ServerKey { get; set; } = string.Empty;
    public string SenderId { get; set; } = string.Empty;
    public string ProjectId { get; set; } = string.Empty;
    public bool IsEnabled { get; set; } = true;
    public int MaxRetries { get; set; } = 3;
    public int TimeoutSeconds { get; set; } = 30;
}

/// <summary>
/// Push authentication request model
/// </summary>
public class PushAuthRequest
{
    public string AuthType { get; set; } = string.Empty;
    public string ServerKey { get; set; } = string.Empty;
    public string ServiceAccountKey { get; set; } = string.Empty;
}

/// <summary>
/// Provider selection request model
/// </summary>
public class ProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public Dictionary<string, string>? Configuration { get; set; }
}
