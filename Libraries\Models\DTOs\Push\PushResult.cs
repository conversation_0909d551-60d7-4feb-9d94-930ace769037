#nullable enable
using System;
using System.Collections.Generic;

namespace Models.DTOs.Push;

/// <summary>
/// Push notification send result
/// </summary>
public class PushResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the push notification was sent successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if sending failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Device token that was targeted
    /// </summary>
    public string? DeviceToken { get; set; }

    /// <summary>
    /// Timestamp when the notification was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// HTTP status code from the push service
    /// </summary>
    public int? StatusCode { get; set; }

    /// <summary>
    /// Response from the push service
    /// </summary>
    public string? ServiceResponse { get; set; }

    /// <summary>
    /// Canonical registration token (for Android)
    /// </summary>
    public string? CanonicalRegistrationToken { get; set; }

    /// <summary>
    /// Whether the device token needs to be updated
    /// </summary>
    public bool ShouldUpdateToken { get; set; }

    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int RetryCount { get; set; }

    /// <summary>
    /// Platform the notification was sent to
    /// </summary>
    public string? Platform { get; set; }

    /// <summary>
    /// Additional metadata from the push service
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// Push notification schedule result
/// </summary>
public class PushScheduleResult
{
    /// <summary>
    /// Unique scheduled message identifier
    /// </summary>
    public string ScheduledMessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Device token that will be targeted
    /// </summary>
    public string? DeviceToken { get; set; }

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
}
