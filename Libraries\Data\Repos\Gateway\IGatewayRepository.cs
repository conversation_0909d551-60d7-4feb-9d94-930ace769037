using Models.DbEntities.Gateway;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Data.Repos.Gateway;

public interface IGatewayConfigurationRepository : IGenericRepository<GatewayConfiguration>
{
    Task<GatewayConfiguration?> GetByGatewayNameAndProviderAsync(string gatewayName, string provider);
    Task<List<GatewayConfiguration>> GetByGatewayTypeAsync(string gatewayType);
    Task<List<GatewayConfiguration>> GetEnabledGatewaysAsync();
    Task<GatewayConfiguration?> GetDefaultGatewayAsync(string gatewayType);
    Task<bool> SetDefaultGatewayAsync(int configurationId, string gatewayType);
}

public interface IGatewayTemplateRepository : IGenericRepository<GatewayTemplate>
{
    Task<List<GatewayTemplate>> GetByGatewayConfigurationIdAsync(int gatewayConfigurationId);
    Task<GatewayTemplate?> GetByTemplateIdAsync(int gatewayConfigurationId, string templateId);
    Task<List<GatewayTemplate>> GetActiveTemplatesAsync(int gatewayConfigurationId);
    Task<bool> DeleteByTemplateIdAsync(int gatewayConfigurationId, string templateId);
}

public interface IGatewayLogRepository : IGenericRepository<GatewayLog>
{
    Task<List<GatewayLog>> GetByMessageIdAsync(string messageId);
    Task<List<GatewayLog>> GetByGatewayAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayLog>> GetByStatusAsync(string status, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayLog>> GetFailedMessagesAsync(DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayLog>> GetByRecipientAsync(string recipient, DateTime? startDate = null, DateTime? endDate = null);
    Task LogMessageAsync(GatewayLog log);
    Task<Dictionary<string, long>> GetMessageCountsByStatusAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);
    Task<Dictionary<string, double>> GetLatencyStatsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);
    Task<List<GatewayLog>> GetRecentLogsAsync(string gatewayName, string provider, int count = 100);
}

public interface IGatewayMetricRepository : IGenericRepository<GatewayMetric>
{
    Task<List<GatewayMetric>> GetByGatewayAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayMetric>> GetByMetricTypeAsync(string metricType, DateTime? startDate = null, DateTime? endDate = null);
    Task<List<GatewayMetric>> GetByPeriodAsync(string period, DateTime startDate, DateTime endDate);
    Task<GatewayMetric?> GetMetricAsync(string gatewayName, string provider, string metricType, string period, DateTime periodStart);
    Task SaveMetricAsync(GatewayMetric metric);
    Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate);
    Task<List<GatewayMetric>> GetLatestMetricsAsync(string gatewayName, string provider, int count = 10);
}
