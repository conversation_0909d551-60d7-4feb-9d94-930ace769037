using Data.Mapping;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using Models.DbEntities.Gateway;

namespace Data.Mapping.Gateway;

public class GatewayConfigurationMapping : MappingEntityTypeConfiguration<GatewayConfiguration>
{
    public override void Configure(EntityTypeBuilder<GatewayConfiguration> builder)
    {
        builder.ToTable("GatewayConfigurations");

        builder.HasKey(x => x.Id);

        builder.Property(x => x.GatewayName)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(x => x.GatewayType)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.Provider)
            .IsRequired()
            .HasMaxLength(50);

        builder.Property(x => x.ConfigurationData)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.RetryConfiguration)
            .HasColumnType("jsonb")
            .HasDefaultValue("{}");

        builder.Property(x => x.IsEnabled)
            .HasDefaultValue(true);

        builder.Property(x => x.IsDefault)
            .HasDefaultValue(false);

        builder.Property(x => x.CreatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.UpdatedAt)
            .HasColumnType("timestamp with time zone")
            .HasDefaultValueSql("CURRENT_TIMESTAMP");

        builder.Property(x => x.CreatedBy)
            .HasMaxLength(100);

        builder.Property(x => x.UpdatedBy)
            .HasMaxLength(100);

        // Indexes
        builder.HasIndex(x => new { x.GatewayName, x.Provider })
            .IsUnique()
            .HasDatabaseName("IX_GatewayConfigurations_GatewayName_Provider");

        builder.HasIndex(x => x.GatewayType)
            .HasDatabaseName("IX_GatewayConfigurations_GatewayType");

        builder.HasIndex(x => x.IsEnabled)
            .HasDatabaseName("IX_GatewayConfigurations_IsEnabled");

        builder.HasIndex(x => x.IsDefault)
            .HasDatabaseName("IX_GatewayConfigurations_IsDefault");

        // Relationships
        builder.HasMany(x => x.Templates)
            .WithOne(x => x.GatewayConfiguration)
            .HasForeignKey(x => x.GatewayConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.Logs)
            .WithOne(x => x.GatewayConfiguration)
            .HasForeignKey(x => x.GatewayConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasMany(x => x.Metrics)
            .WithOne(x => x.GatewayConfiguration)
            .HasForeignKey(x => x.GatewayConfigurationId)
            .OnDelete(DeleteBehavior.Cascade);
    }
}
