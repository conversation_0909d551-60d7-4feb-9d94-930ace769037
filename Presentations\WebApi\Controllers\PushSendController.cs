#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Push;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Push notification sending endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class PushSendController : ControllerBase
{
    private readonly IMessageSender<PushPayload, PushResult> _pushSender;
    private readonly IMessageScheduler<PushPayload, PushScheduleResult> _pushScheduler;

    public PushSendController(
        IMessageSender<PushPayload, PushResult> pushSender,
        IMessageScheduler<PushPayload, PushScheduleResult> pushScheduler)
    {
        _pushSender = pushSender;
        _pushScheduler = pushScheduler;
    }

    /// <summary>
    /// Send a single push notification
    /// </summary>
    /// <param name="payload">Push notification payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Send result</returns>
    /// <response code="200">Push notification sent successfully</response>
    /// <response code="400">Invalid payload</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(PushResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<PushResult>> Send([FromBody] PushPayload payload, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _pushSender.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Send multiple push notifications in bulk
    /// </summary>
    /// <param name="payloads">List of push notification payloads</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk send results</returns>
    /// <response code="200">Push notifications sent successfully</response>
    /// <response code="400">Invalid payloads</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("sendbulk")]
    [ProducesResponseType(typeof(IReadOnlyList<PushResult>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<IReadOnlyList<PushResult>>> SendBulk([FromBody] IEnumerable<PushPayload> payloads, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var results = await _pushSender.SendBulkAsync(payloads, cancellationToken);
        return Ok(results);
    }

    /// <summary>
    /// Schedule a push notification for future delivery
    /// </summary>
    /// <param name="request">Schedule request containing payload and delivery time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Schedule result</returns>
    /// <response code="200">Push notification scheduled successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("schedulesend")]
    [ProducesResponseType(typeof(PushScheduleResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<PushScheduleResult>> ScheduleSend([FromBody] SchedulePushRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _pushScheduler.ScheduleMessageAsync(request.Payload, request.ScheduledTime, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get status of a sent push notification
    /// </summary>
    /// <param name="messageId">Message ID to check status for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message status</returns>
    /// <response code="200">Status retrieved successfully</response>
    /// <response code="404">Message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<object>> GetStatus(string messageId, CancellationToken cancellationToken)
    {
        var status = await _pushSender.GetStatusAsync(messageId, cancellationToken);
        return Ok(status);
    }

    /// <summary>
    /// Resend a previously sent push notification
    /// </summary>
    /// <param name="messageId">Original message ID to resend</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Resend result</returns>
    /// <response code="200">Push notification resent successfully</response>
    /// <response code="404">Original message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("resend/{messageId}")]
    [ProducesResponseType(typeof(PushResult), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<PushResult>> Resend(string messageId, CancellationToken cancellationToken)
    {
        var result = await _pushSender.ResendAsync(messageId, cancellationToken);
        return Ok(result);
    }
}

/// <summary>
/// Request model for scheduling push notifications
/// </summary>
public class SchedulePushRequest
{
    /// <summary>
    /// Push notification payload
    /// </summary>
    public PushPayload Payload { get; set; } = new();

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTimeOffset ScheduledTime { get; set; }
}
