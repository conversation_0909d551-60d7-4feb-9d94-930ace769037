﻿using Identity.Models;
using Microsoft.AspNetCore.Identity;
using Models.Enums;
using System.Linq;
using System.Threading.Tasks;

namespace Identity.Seeds;

public static class DefaultSuperAdmin
{
    public static async Task SeedAsync(UserManager<ApplicationUser> userManager)
    {
        // Seed Default Super Admin User
        var defaultEmail = "<EMAIL>";
        var defaultUserName = "blackbee";

        // Check if user already exists by email or username
        var existingUser = await userManager.FindByEmailAsync(defaultEmail);
        if (existingUser != null)
        {
            await EnsureUserHasRoles(userManager, existingUser);
            return;
        }

        existingUser = await userManager.FindByNameAsync(defaultUserName);
        if (existingUser != null)
        {
            // User exists with different email, ensure they have all required roles
            await EnsureUserHasRoles(userManager, existingUser);
            return;
        }

        // Create new super admin user
        var defaultUser = new ApplicationUser
        {
            UserName = defaultUserName,
            Email = defaultEmail,
            FirstName = "blackbee",
            LastName = "blackbee",
            EmailConfirmed = true,
            PhoneNumberConfirmed = true
        };

        var result = await userManager.CreateAsync(defaultUser, "blackbee123!");
        if (result.Succeeded)
        {
            await EnsureUserHasRoles(userManager, defaultUser);
        }
        else
        {
            var errors = string.Join(", ", result.Errors.Select(e => e.Description));
            Console.WriteLine($"Failed to create super admin user: {errors}");
        }
    }

    private static async Task EnsureUserHasRoles(UserManager<ApplicationUser> userManager, ApplicationUser user)
    {
        var requiredRoles = new[]
        {
            Roles.Basic.ToString(),
            Roles.Moderator.ToString(),
            Roles.Admin.ToString(),
            Roles.SuperAdmin.ToString()
        };

        foreach (var role in requiredRoles)
        {
            if (!await userManager.IsInRoleAsync(user, role))
            {
                var result = await userManager.AddToRoleAsync(user, role);
                if (!result.Succeeded)
                {
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    Console.WriteLine($"Failed to add role '{role}' to user '{user.UserName}': {errors}");
                }
            }
        }
    }
}