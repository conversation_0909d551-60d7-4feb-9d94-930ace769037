#nullable enable
using System;
using System.Collections.Generic;

namespace Models.DTOs.InApp;

/// <summary>
/// In-app notification send result
/// </summary>
public class InAppResult
{
    /// <summary>
    /// Unique notification identifier
    /// </summary>
    public string NotificationId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the notification was created successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if creation failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Target user ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Notification type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Timestamp when the notification was created
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Notification status (pending, delivered, read, dismissed, expired)
    /// </summary>
    public string Status { get; set; } = "pending";

    /// <summary>
    /// Channels the notification was sent to
    /// </summary>
    public List<string>? Channels { get; set; }

    /// <summary>
    /// Whether the notification is persistent
    /// </summary>
    public bool IsPersistent { get; set; }

    /// <summary>
    /// Expiration time
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Priority level
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }
}

/// <summary>
/// In-app notification schedule result
/// </summary>
public class InAppScheduleResult
{
    /// <summary>
    /// Unique scheduled notification identifier
    /// </summary>
    public string ScheduledNotificationId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Target user ID
    /// </summary>
    public string? UserId { get; set; }

    /// <summary>
    /// Notification type
    /// </summary>
    public string? Type { get; set; }

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Priority level
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Whether the scheduled notification can be cancelled
    /// </summary>
    public bool CanCancel { get; set; } = true;
}
