#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.MessengerApp;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Messenger app sending endpoints (WhatsApp, Facebook Messenger, Telegram)
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class MessengerAppSendController : ControllerBase
{
    private readonly IMessageSender<MessengerAppPayload, MessengerAppResult> _messengerSender;
    private readonly IMessageScheduler<MessengerAppPayload, MessengerAppScheduleResult> _messengerScheduler;

    public MessengerAppSendController(
        IMessageSender<MessengerAppPayload, MessengerAppResult> messengerSender,
        IMessageScheduler<MessengerAppPayload, MessengerAppScheduleResult> messengerScheduler)
    {
        _messengerSender = messengerSender;
        _messengerScheduler = messengerScheduler;
    }

    /// <summary>
    /// Send a single messenger app message
    /// </summary>
    [HttpPost("send")]
    public async Task<ActionResult<MessengerAppResult>> Send([FromBody] MessengerAppPayload payload, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var result = await _messengerSender.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Send multiple messenger app messages in bulk
    /// </summary>
    [HttpPost("sendbulk")]
    public async Task<ActionResult<IReadOnlyList<MessengerAppResult>>> SendBulk([FromBody] IEnumerable<MessengerAppPayload> payloads, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var results = await _messengerSender.SendBulkAsync(payloads, cancellationToken);
        return Ok(results);
    }

    /// <summary>
    /// Schedule a messenger app message for future delivery
    /// </summary>
    [HttpPost("schedulesend")]
    public async Task<ActionResult<MessengerAppScheduleResult>> ScheduleSend([FromBody] ScheduleMessengerAppRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var result = await _messengerScheduler.ScheduleMessageAsync(request.Payload, request.ScheduledTime, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get status of a sent messenger app message
    /// </summary>
    [HttpGet("status/{messageId}")]
    public async Task<ActionResult<object>> GetStatus(string messageId, CancellationToken cancellationToken)
    {
        var status = await _messengerSender.GetStatusAsync(messageId, cancellationToken);
        return Ok(status);
    }

    /// <summary>
    /// Get conversation history
    /// </summary>
    [HttpGet("conversation/{recipientId}")]
    public async Task<ActionResult<object>> GetConversation(string recipientId, [FromQuery] int limit = 50, CancellationToken cancellationToken = default)
    {
        return Ok(new
        {
            RecipientId = recipientId,
            Messages = new[]
            {
                new { Id = "msg_001", Direction = "outbound", Content = "Hello! How can I help you?", Timestamp = DateTime.UtcNow.AddHours(-2), Status = "delivered" },
                new { Id = "msg_002", Direction = "inbound", Content = "I need help with my order", Timestamp = DateTime.UtcNow.AddHours(-1), Status = "read" },
                new { Id = "msg_003", Direction = "outbound", Content = "I'd be happy to help! What's your order number?", Timestamp = DateTime.UtcNow.AddMinutes(-30), Status = "delivered" }
            },
            TotalCount = 3,
            HasMore = false
        });
    }

    /// <summary>
    /// Send typing indicator
    /// </summary>
    [HttpPost("typing/{recipientId}")]
    public async Task<ActionResult<object>> SendTypingIndicator(string recipientId, [FromBody] TypingIndicatorRequest request, CancellationToken cancellationToken)
    {
        return Ok(new
        {
            Success = true,
            RecipientId = recipientId,
            Action = request.Action, // "typing_on" or "typing_off"
            Timestamp = DateTime.UtcNow
        });
    }

    /// <summary>
    /// Mark message as read
    /// </summary>
    [HttpPost("read/{messageId}")]
    public async Task<ActionResult<object>> MarkAsRead(string messageId, CancellationToken cancellationToken)
    {
        return Ok(new
        {
            Success = true,
            MessageId = messageId,
            ReadAt = DateTime.UtcNow
        });
    }
}

public class ScheduleMessengerAppRequest
{
    public MessengerAppPayload Payload { get; set; } = new();
    public DateTimeOffset ScheduledTime { get; set; }
}

public class TypingIndicatorRequest
{
    public string Action { get; set; } = "typing_on"; // "typing_on" or "typing_off"
}
