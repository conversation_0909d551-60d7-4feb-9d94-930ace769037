﻿using AutoMapper;
using Models.DbEntities;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Log;
using Models.ResponseModels;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Logging endpoints for retrieving user authentication logs and analytics
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class LogController : ControllerBase
{
    private readonly ILoginLogService _loginLogService;
    private readonly IMapper _mapper;
    public LogController( ILoginLogService loginLogService, IMapper mapper)
    {
        _loginLogService = loginLogService;
        _mapper = mapper;
    }

    /// <summary>
    /// Get user authentication logs
    /// </summary>
    /// <param name="email">User email to retrieve logs for</param>
    /// <returns>List of authentication logs for the specified user</returns>
    /// <response code="200">Logs retrieved successfully</response>
    /// <response code="400">Invalid email parameter</response>
    [HttpGet("get")]
    [ProducesResponseType(typeof(BaseResponse<IReadOnlyList<LogDto>>), 200)]
    [ProducesResponseType(400)]
    public async Task<IActionResult> GetUserAuthLogs(string email)
    {
        var userList = await _loginLogService.Get(email);
        var data = _mapper
            .Map<IReadOnlyList<LoginLog>, IReadOnlyList<LogDto>>(userList);

        return Ok(new BaseResponse<IReadOnlyList<LogDto>>(data, $"User Log List"));
    }
}