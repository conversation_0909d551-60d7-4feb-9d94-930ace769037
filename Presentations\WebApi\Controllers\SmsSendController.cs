#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.SMS;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// SMS sending endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class SmsSendController : ControllerBase
{
    private readonly IMessageSender<SmsPayload, SmsResult> _smsSender;
    private readonly IMessageScheduler<SmsPayload, SmsScheduleResult> _smsScheduler;

    public SmsSendController(
        IMessageSender<SmsPayload, SmsResult> smsSender,
        IMessageScheduler<SmsPayload, SmsScheduleResult> smsScheduler)
    {
        _smsSender = smsSender;
        _smsScheduler = smsScheduler;
    }

    /// <summary>
    /// Send a single SMS message
    /// </summary>
    /// <param name="payload">SMS payload</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Send result</returns>
    /// <response code="200">SMS sent successfully</response>
    /// <response code="400">Invalid payload</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("send")]
    [ProducesResponseType(typeof(SmsResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<SmsResult>> Send([FromBody] SmsPayload payload, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _smsSender.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Send multiple SMS messages in bulk
    /// </summary>
    /// <param name="payloads">List of SMS payloads</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Bulk send results</returns>
    /// <response code="200">SMS messages sent successfully</response>
    /// <response code="400">Invalid payloads</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("sendbulk")]
    [ProducesResponseType(typeof(IReadOnlyList<SmsResult>), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<IReadOnlyList<SmsResult>>> SendBulk([FromBody] IEnumerable<SmsPayload> payloads, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var results = await _smsSender.SendBulkAsync(payloads, cancellationToken);
        return Ok(results);
    }

    /// <summary>
    /// Schedule an SMS message for future delivery
    /// </summary>
    /// <param name="request">Schedule request containing payload and delivery time</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Schedule result</returns>
    /// <response code="200">SMS scheduled successfully</response>
    /// <response code="400">Invalid request</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("schedulesend")]
    [ProducesResponseType(typeof(SmsScheduleResult), 200)]
    [ProducesResponseType(400)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<SmsScheduleResult>> ScheduleSend([FromBody] ScheduleSmsRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _smsScheduler.ScheduleMessageAsync(request.Payload, request.ScheduledTime, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get status of a sent SMS message
    /// </summary>
    /// <param name="messageId">Message ID to check status for</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Message status</returns>
    /// <response code="200">Status retrieved successfully</response>
    /// <response code="404">Message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpGet("status/{messageId}")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<object>> GetStatus(string messageId, CancellationToken cancellationToken)
    {
        var status = await _smsSender.GetStatusAsync(messageId, cancellationToken);
        return Ok(status);
    }

    /// <summary>
    /// Resend a previously sent SMS message
    /// </summary>
    /// <param name="messageId">Original message ID to resend</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Resend result</returns>
    /// <response code="200">SMS resent successfully</response>
    /// <response code="404">Original message not found</response>
    /// <response code="401">Unauthorized</response>
    [HttpPost("resend/{messageId}")]
    [ProducesResponseType(typeof(SmsResult), 200)]
    [ProducesResponseType(404)]
    [ProducesResponseType(401)]
    public async Task<ActionResult<SmsResult>> Resend(string messageId, CancellationToken cancellationToken)
    {
        var result = await _smsSender.ResendAsync(messageId, cancellationToken);
        return Ok(result);
    }
}

/// <summary>
/// Request model for scheduling SMS messages
/// </summary>
public class ScheduleSmsRequest
{
    /// <summary>
    /// SMS payload
    /// </summary>
    public SmsPayload Payload { get; set; } = new();

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTimeOffset ScheduledTime { get; set; }
}
