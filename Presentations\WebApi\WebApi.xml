<?xml version="1.0"?>
<doc>
    <assembly>
        <name>WebApi</name>
    </assembly>
    <members>
        <member name="T:WebApi.Controllers.AccountController">
            <summary>
            Account management endpoints for user authentication, registration, and password management
            </summary>
        </member>
        <member name="M:WebApi.Controllers.AccountController.AuthenticateAsync(Models.DTOs.Account.AuthenticationRequest)">
            <summary>
            Authenticate user and return JWT token
            </summary>
            <param name="request">User credentials (email and password)</param>
            <returns>JWT token and user information</returns>
            <response code="200">Authentication successful - returns JWT token</response>
            <response code="400">Invalid credentials or validation errors</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.RegisterAsync(Models.DTOs.Account.RegisterRequest)">
            <summary>
            Register a new user account
            </summary>
            <param name="request">User registration details</param>
            <returns>Registration result with confirmation details</returns>
            <response code="200">Registration successful</response>
            <response code="400">Validation errors or registration failed</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.ConfirmEmailAsync(System.String,System.String)">
            <summary>
            Confirm user email address
            </summary>
            <param name="userId">User ID from confirmation email</param>
            <param name="code">Confirmation code from email</param>
            <returns>Email confirmation result</returns>
            <response code="200">Email confirmed successfully</response>
            <response code="400">Invalid confirmation code or user ID</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.ForgotPasswordAsync(Models.DTOs.Account.ForgotPasswordRequest)">
            <summary>
            Request password reset for user account
            </summary>
            <param name="request">Forgot password request with email address</param>
            <returns>Password reset confirmation</returns>
            <response code="200">Password reset email sent successfully</response>
            <response code="400">Invalid email or request failed</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.ResetPasswordAsync(Models.DTOs.Account.ResetPasswordRequest)">
            <summary>
            Reset user password using reset token
            </summary>
            <param name="request">Reset password request with token and new password</param>
            <returns>Password reset result</returns>
            <response code="200">Password reset successfully</response>
            <response code="400">Invalid token or reset failed</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.RefreshTokenAsync(Models.DTOs.Account.RefreshTokenRequest)">
            <summary>
            Refresh JWT access token using refresh token
            </summary>
            <param name="request">Refresh token request</param>
            <returns>New JWT access token</returns>
            <response code="200">Token refreshed successfully</response>
            <response code="400">Invalid refresh token</response>
        </member>
        <member name="M:WebApi.Controllers.AccountController.LogoutAsync(System.String)">
            <summary>
            Logout user and invalidate tokens
            </summary>
            <param name="userEmail">User email address to logout</param>
            <returns>Logout confirmation</returns>
            <response code="200">User logged out successfully</response>
            <response code="400">Logout failed</response>
        </member>
        <member name="T:WebApi.Controllers.AdminController">
            <summary>
            Administrative endpoints for user management (requires admin privileges)
            </summary>
        </member>
        <member name="M:WebApi.Controllers.AdminController.GetAllUser">
            <summary>
            Get all users (Admin only)
            </summary>
            <returns>List of all users without role information</returns>
            <response code="200">Users retrieved successfully</response>
            <response code="401">Unauthorized - requires authentication</response>
            <response code="403">Forbidden - requires admin role</response>
        </member>
        <member name="M:WebApi.Controllers.AdminController.GetAllUserWithRoles">
            <summary>
            Get all users with their roles (SuperAdmin only)
            </summary>
            <returns>List of all users with their assigned roles</returns>
            <response code="200">Users with roles retrieved successfully</response>
            <response code="401">Unauthorized - requires authentication</response>
            <response code="403">Forbidden - requires SuperAdmin role</response>
        </member>
        <member name="T:WebApi.Controllers.GraphQLController">
            <summary>
            GraphQL endpoint for flexible API queries and mutations
            </summary>
        </member>
        <member name="M:WebApi.Controllers.GraphQLController.#ctor(GraphQL.IDocumentExecuter,GraphQL.Types.ISchema)">
            <summary>
            GraphQL endpoint for flexible API queries and mutations
            </summary>
        </member>
        <member name="M:WebApi.Controllers.GraphQLController.Post(WebApi.GraphQL.GraphQlQuery)">
            <summary>
            Execute GraphQL query or mutation
            </summary>
            <param name="query">GraphQL query object with query string and variables</param>
            <returns>GraphQL execution result</returns>
            <response code="200">Query executed successfully</response>
            <response code="400">Invalid query or execution error</response>
        </member>
        <member name="T:WebApi.Controllers.LogController">
            <summary>
            Logging endpoints for retrieving user authentication logs and analytics
            </summary>
        </member>
        <member name="M:WebApi.Controllers.LogController.GetUserAuthLogs(System.String)">
            <summary>
            Get user authentication logs
            </summary>
            <param name="email">User email to retrieve logs for</param>
            <returns>List of authentication logs for the specified user</returns>
            <response code="200">Logs retrieved successfully</response>
            <response code="400">Invalid email parameter</response>
        </member>
        <member name="T:WebApi.Controllers.NotificationController">
            <summary>
            Notification endpoints for sending messages via multiple channels (WhatsApp, Push Notifications)
            </summary>
        </member>
        <member name="M:WebApi.Controllers.NotificationController.SendWhatsAppMessage(Models.DTOs.WhatsApp.WhatsAppPayload,System.Threading.CancellationToken)">
            <summary>
            Send WhatsApp message
            </summary>
            <param name="payload">WhatsApp message payload with recipient and content</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>WhatsApp send result</returns>
            <response code="200">Message sent successfully</response>
            <response code="400">Invalid payload or send failed</response>
        </member>
        <member name="M:WebApi.Controllers.NotificationController.SendPushNotification(Models.DTOs.Push.PushNotificationPayload,System.Threading.CancellationToken)">
            <summary>
            Send push notification
            </summary>
            <param name="payload">Push notification payload with device tokens and content</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Push notification send result</returns>
            <response code="200">Notification sent successfully</response>
            <response code="400">Invalid payload or send failed</response>
        </member>
        <member name="T:WebApi.Controllers.SmsController">
            <summary>
            SMS messaging endpoints for sending text messages
            </summary>
        </member>
        <member name="M:WebApi.Controllers.SmsController.SendAsync(Models.DTOs.SMS.SmsPayload,System.Threading.CancellationToken)">
            <summary>
            Send a single SMS message
            </summary>
            <param name="payload">SMS message payload with recipient and content</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>SMS send result</returns>
            <response code="200">SMS message sent successfully</response>
            <response code="400">Invalid payload or send failed</response>
        </member>
        <member name="M:WebApi.Controllers.SmsController.SendBulkAsync(System.Collections.Generic.IEnumerable{Models.DTOs.SMS.SmsPayload},System.Threading.CancellationToken)">
            <summary>
            Send SMS messages in bulk
            </summary>
            <param name="payloads">Collection of SMS payloads to send</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Collection of SMS send results</returns>
            <response code="200">SMS messages sent successfully</response>
            <response code="400">Invalid payloads or send failed</response>
        </member>
    </members>
</doc>
