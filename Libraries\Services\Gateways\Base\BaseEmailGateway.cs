using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.Email;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Services.Gateway;

namespace Services.Gateways.Base
{
    public abstract class BaseEmailGateway : BaseGateway, IEmailGateway
    {
        protected BaseEmailGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName)
            : base(httpClient, dataService, gatewayName)
        {
        }

        // IGateway interface implementations
        public abstract string ProviderName { get; }
        public virtual bool IsEnabled => true;
        public virtual Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        // Abstract methods that must be implemented by derived classes
        public abstract Task<EmailResult> SendAsync(EmailPayload payload, CancellationToken cancellationToken = default);
        public abstract Task<IReadOnlyList<EmailResult>> SendBulkAsync(IEnumerable<EmailPayload> payloads, CancellationToken cancellationToken = default);
        public abstract Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
        public abstract Task<EmailScheduleResult> ScheduleMessageAsync(EmailPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
        public abstract Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
        public abstract Task<EmailScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, EmailPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
        public abstract Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);
        public abstract GatewayCapabilities GetCapabilities();

        // Virtual methods with default implementations
        public virtual async Task<EmailResult> SendWithTemplateAsync(string templateId, object templateData, string recipient, CancellationToken cancellationToken = default)
        {
            var payload = new EmailPayload
            {
                To = recipient,
                TemplateId = templateId,
                TemplateData = templateData
            };
            return await SendAsync(payload, cancellationToken);
        }

        public virtual async Task<IReadOnlyList<EmailResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipient)> recipients, CancellationToken cancellationToken = default)
        {
            var payloads = recipients.Select(r => new EmailPayload
            {
                To = r.recipient,
                TemplateId = templateId,
                TemplateData = r.templateData
            });
            return await SendBulkAsync(payloads, cancellationToken);
        }

        public virtual async Task<EmailValidationResult> ValidateEmailAsync(string email, CancellationToken cancellationToken = default)
        {
            var isValid = !string.IsNullOrWhiteSpace(email) && email.Contains("@");
            return new EmailValidationResult
            {
                Email = email,
                IsValid = isValid,
                ValidationDetails = new Dictionary<string, object>
                {
                    ["BasicFormat"] = isValid,
                    ["ValidatedAt"] = DateTime.UtcNow
                }
            };
        }

        public virtual async Task<EmailDeliveryReport> GetDeliveryReportAsync(string messageId, CancellationToken cancellationToken = default)
        {
            var status = await GetStatusAsync(messageId, cancellationToken);
            return new EmailDeliveryReport
            {
                MessageId = messageId,
                Status = status.Status,
                DeliveredAt = status.UpdatedAt,
                Events = new List<EmailEvent>
                {
                    new EmailEvent
                    {
                        Type = "sent",
                        Timestamp = status.UpdatedAt ?? DateTime.UtcNow,
                        Details = new Dictionary<string, object> { ["status"] = status.Status }
                    }
                }
            };
        }

        protected virtual async Task LogOperationAsync(string operation, string messageId, string status, object payload = null, string errorMessage = null, CancellationToken cancellationToken = default)
        {
            await _dataService.LogMessageAsync(messageId, _gatewayName, "Email", operation, status, 
                requestPayload: payload, responseData: null, errorMessage: errorMessage, 
                errorCode: null, latencyMs: null, recipient: null, subject: null, messageType: "email", 
                retryCount: 0, additionalInfo: null, gatewayConfigurationId: null);
        }

        protected virtual string GenerateMessageId()
        {
            return $"{_gatewayName}_{Guid.NewGuid():N}";
        }

        protected virtual void ValidatePayload(EmailPayload payload)
        {
            if (payload == null)
                throw new ArgumentNullException(nameof(payload));
            
            if (string.IsNullOrWhiteSpace(payload.To))
                throw new ArgumentException("Recipient email is required", nameof(payload));
            
            if (string.IsNullOrWhiteSpace(payload.Subject) && string.IsNullOrWhiteSpace(payload.TemplateId))
                throw new ArgumentException("Either subject or template ID is required", nameof(payload));
        }

        protected virtual async Task<bool> ShouldRetryAsync(Exception exception, int attemptNumber)
        {
            if (attemptNumber >= 3) return false;
            
            return exception is HttpRequestException || 
                   exception is TaskCanceledException ||
                   (exception is HttpRequestException httpEx && httpEx.Message.Contains("5"));
        }

        protected virtual TimeSpan GetRetryDelay(int attemptNumber)
        {
            return TimeSpan.FromSeconds(Math.Pow(2, attemptNumber - 1));
        }

        protected virtual void Dispose(bool disposing)
        {
            if (disposing)
            {
                _httpClient?.Dispose();
            }
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
