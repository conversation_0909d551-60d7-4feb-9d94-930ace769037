#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Webhook metrics and analytics endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class WebhookMetricsController : ControllerBase
{
    /// <summary>
    /// Get webhook usage metrics
    /// </summary>
    [HttpGet("usage")]
    public async Task<object> Usage([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalWebhooks = 8450,
            UniqueEndpoints = 125,
            TotalEvents = 15,
            MethodBreakdown = new { POST = 7200, GET = 850, PUT = 300, DELETE = 100 },
            EventBreakdown = new[]
            {
                new { Event = "user.created", Count = 2100 },
                new { Event = "order.placed", Count = 1800 },
                new { Event = "payment.received", Count = 1500 }
            }
        });
    }

    /// <summary>
    /// Get webhook success rate metrics
    /// </summary>
    [HttpGet("successrate")]
    public async Task<object> SuccessRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallSuccessRate = 96.8,
            TotalSent = 8450,
            TotalSuccessful = 8179,
            StatusCodeBreakdown = new
            {
                Success2xx = 8179,
                ClientError4xx = 156,
                ServerError5xx = 115
            }
        });
    }

    /// <summary>
    /// Get webhook failure rate metrics
    /// </summary>
    [HttpGet("failurerate")]
    public async Task<object> FailureRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallFailureRate = 3.2,
            TotalFailed = 271,
            FailureReasons = new[]
            {
                new { Reason = "Timeout", Count = 98, Percentage = 36.2 },
                new { Reason = "Connection Error", Count = 67, Percentage = 24.7 },
                new { Reason = "HTTP 500", Count = 45, Percentage = 16.6 },
                new { Reason = "HTTP 404", Count = 34, Percentage = 12.5 },
                new { Reason = "Other", Count = 27, Percentage = 10.0 }
            }
        });
    }

    /// <summary>
    /// Get webhook latency metrics
    /// </summary>
    [HttpGet("latency")]
    public async Task<object> Latency([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            AverageLatencyMs = 1250,
            MedianLatencyMs = 890,
            P95LatencyMs = 3200,
            P99LatencyMs = 8500,
            MinLatencyMs = 125,
            MaxLatencyMs = 29000
        });
    }

    /// <summary>
    /// Get webhook delivery count metrics
    /// </summary>
    [HttpGet("deliverycount")]
    public async Task<object> DeliveryCount([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalDelivered = 8179,
            TotalPending = 45,
            TotalFailed = 271,
            DeliveryRate = 96.8,
            RetryStatistics = new
            {
                TotalRetries = 189,
                SuccessfulRetries = 134,
                RetrySuccessRate = 70.9
            }
        });
    }
}
