﻿using System.Collections.Generic;
using System.Threading.Tasks;
using Data.Repos;
using Models.DbEntities;
using Services.Interfaces;

namespace Services.Concrete;

public class LoginLogService : ILoginLogService
{
    private readonly ILoginLogRepository _repository;

    public LoginLogService(ILoginLogRepository repository)
    {
        _repository = repository;
    }

    public async Task Add(LoginLog model)
    {
        await _repository.AddAsync(model);
    }

    public async Task<List<LoginLog>> Get(string email)
    {
        return await _repository.GetByUserEmailAsync(email);
    }
}