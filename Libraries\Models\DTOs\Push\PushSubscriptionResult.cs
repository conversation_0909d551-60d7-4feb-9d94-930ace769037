using System;
using System.Collections.Generic;

namespace Models.DTOs.Push
{
    /// <summary>
    /// Result of subscribing/unsubscribing a device to/from a topic
    /// </summary>
    public class PushSubscriptionResult
    {
        /// <summary>
        /// Whether the subscription operation was successful
        /// </summary>
        public bool IsSuccess { get; set; }

        /// <summary>
        /// Device token that was subscribed/unsubscribed
        /// </summary>
        public string DeviceToken { get; set; } = string.Empty;

        /// <summary>
        /// Topic name
        /// </summary>
        public string Topic { get; set; } = string.Empty;

        /// <summary>
        /// Operation type (subscribe, unsubscribe)
        /// </summary>
        public string Operation { get; set; } = string.Empty;

        /// <summary>
        /// Platform (FCM, APNS, OneSignal, etc.)
        /// </summary>
        public string Platform { get; set; } = string.Empty;

        /// <summary>
        /// When the operation was performed
        /// </summary>
        public DateTime OperationAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Error message if operation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// Error code if applicable
        /// </summary>
        public string? ErrorCode { get; set; }

        /// <summary>
        /// Gateway that processed the operation
        /// </summary>
        public string? GatewayName { get; set; }

        /// <summary>
        /// Platform-specific response data
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Raw platform response
        /// </summary>
        public object? PlatformResponse { get; set; }

        /// <summary>
        /// Number of devices affected (for bulk operations)
        /// </summary>
        public int? DevicesAffected { get; set; }

        /// <summary>
        /// List of failed device tokens (for bulk operations)
        /// </summary>
        public List<string> FailedTokens { get; set; } = new();
    }
}
