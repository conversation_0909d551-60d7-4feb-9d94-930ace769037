#nullable enable
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Interfaces;

public interface IMessageSender<TPayload, TResult>
{
    Task<TResult> SendAsync(TPayload payload, CancellationToken cancellationToken = default);
    Task<IReadOnlyList<TResult>> SendBulkAsync(IEnumerable<TPayload> payloads, CancellationToken cancellationToken = default);
    Task<TResult> ResendAsync(string originalMessageId, CancellationToken cancellationToken = default);
    Task<object> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
    Task<object> GetDeliveryReceiptAsync(string messageId, CancellationToken cancellationToken = default);
    Task<object> SendRawPayloadAsync(object rawPayload, CancellationToken cancellationToken = default);
    Task<TPayload> PrepareMessageAsync(TPayload payload, CancellationToken cancellationToken = default);
    Task<bool> IsAvailableAsync(CancellationToken cancellationToken = default);
    Task<string?> GetTemplateAsync(string templateId, CancellationToken cancellationToken = default);
}
