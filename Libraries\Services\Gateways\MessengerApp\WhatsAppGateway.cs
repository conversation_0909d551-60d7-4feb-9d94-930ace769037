#nullable enable
using Models.DTOs.MessengerApp;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Libraries.Services.Gateways.Utils;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;

namespace Services.Gateways.MessengerApp;

/// <summary>
/// WhatsApp Business API gateway implementation
/// </summary>
public class WhatsAppGateway : IMessageGateway<MessengerAppPayload, MessengerAppResult>, IAdminGateway, IMetricsGateway, ISchedulableGateway<MessengerAppPayload, MessengerAppScheduleResult>
{
    private readonly HttpClient _httpClient;
    private string _accessToken = string.Empty;
    private string _phoneNumberId = string.Empty;
    private string _businessAccountId = string.Empty;
    private bool _isInitialized = false;

    public string ProviderName => "WhatsApp Business API";
    public bool IsEnabled => _isInitialized && !string.IsNullOrEmpty(_accessToken) && !string.IsNullOrEmpty(_phoneNumberId);

    public WhatsAppGateway(HttpClient httpClient)
    {
        _httpClient = httpClient;
    }

    public async Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        if (configuration.TryGetValue("AccessToken", out var accessToken))
            _accessToken = accessToken;
        
        if (configuration.TryGetValue("PhoneNumberId", out var phoneNumberId))
            _phoneNumberId = phoneNumberId;

        if (configuration.TryGetValue("BusinessAccountId", out var businessAccountId))
            _businessAccountId = businessAccountId;

        // Set up bearer token authentication
        if (!string.IsNullOrEmpty(_accessToken))
        {
            _httpClient.DefaultRequestHeaders.Clear();
            _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_accessToken}");
        }

        _isInitialized = true;
        await Task.CompletedTask;
    }

    public async Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;
        try
        {
            // Test by getting phone number info
            var response = await _httpClient.GetAsync($"https://graph.facebook.com/v18.0/{_phoneNumberId}", cancellationToken);
            
            return new GatewayHealthResult
            {
                IsHealthy = response.IsSuccessStatusCode,
                ResponseTime = DateTime.UtcNow - startTime,
                AdditionalInfo = new Dictionary<string, object>
                {
                    ["StatusCode"] = (int)response.StatusCode,
                    ["PhoneNumberId"] = _phoneNumberId
                }
            };
        }
        catch (Exception ex)
        {
            return new GatewayHealthResult
            {
                IsHealthy = false,
                ErrorMessage = ex.Message,
                ResponseTime = DateTime.UtcNow - startTime
            };
        }
    }

    public GatewayCapabilities GetCapabilities()
    {
        return new GatewayCapabilities
        {
            SupportsBulkSending = true,
            SupportsScheduling = false, // WhatsApp doesn't support native scheduling
            SupportsDeliveryReceipts = true,
            SupportsReadReceipts = true,
            SupportsTemplates = true,
            SupportsAttachments = true,
            SupportsRichContent = true,
            MaxMessageSize = 4096,
            MaxBulkSize = 100,
            RateLimitPerMinute = 1000,
            SupportedContentTypes = new List<string> { "text", "image", "video", "audio", "document", "location", "contact", "template" },
            SupportedFeatures = new List<string> { "Interactive Messages", "Templates", "Media", "Location", "Contacts", "Buttons", "Lists" }
        };
    }

    public async Task<MessengerAppResult> SendAsync(MessengerAppPayload payload, CancellationToken cancellationToken = default)
    {
        try
        {
            var whatsappPayload = ConvertToWhatsAppPayload(payload);
            var json = JsonSerializer.Serialize(whatsappPayload);
            var content = new StringContent(json, Encoding.UTF8, "application/json");

            var response = await _httpClient.PostAsync($"https://graph.facebook.com/v18.0/{_phoneNumberId}/messages", content, cancellationToken);
            var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);

            if (response.IsSuccessStatusCode)
            {
                var whatsappResponse = JsonSerializer.Deserialize<WhatsAppResponse>(responseContent);
                return GatewayResultHelper.CreateMessengerAppSuccessResult(payload,
                    whatsappResponse?.messages?.FirstOrDefault()?.id ?? Guid.NewGuid().ToString(),
                    responseContent, 200, "WhatsApp");
            }
            else
            {
                var errorResponse = JsonSerializer.Deserialize<WhatsAppErrorResponse>(responseContent);
                return GatewayResultHelper.CreateMessengerAppErrorResult(payload,
                    errorResponse?.error?.message ?? $"WhatsApp API error: {response.StatusCode}",
                    (int)response.StatusCode, null);
            }
        }
        catch (Exception ex)
        {
            return GatewayResultHelper.CreateMessengerAppErrorResult(payload, ex.Message, 500, null);
        }
    }

    public async Task<IReadOnlyList<MessengerAppResult>> SendBulkAsync(IEnumerable<MessengerAppPayload> payloads, CancellationToken cancellationToken = default)
    {
        var results = new List<MessengerAppResult>();
        var tasks = payloads.Select(payload => SendAsync(payload, cancellationToken));
        
        var completedResults = await Task.WhenAll(tasks);
        results.AddRange(completedResults);

        return results.AsReadOnly();
    }

    public async Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default)
    {
        try
        {
            var response = await _httpClient.GetAsync($"https://graph.facebook.com/v18.0/{messageId}", cancellationToken);
            
            if (response.IsSuccessStatusCode)
            {
                var responseContent = await response.Content.ReadAsStringAsync(cancellationToken);
                var message = JsonSerializer.Deserialize<WhatsAppMessageStatus>(responseContent);
                
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = ConvertWhatsAppStatus(message?.status ?? "unknown"),
                    SentAt = message?.timestamp,
                    AdditionalInfo = new Dictionary<string, object>
                    {
                        ["Provider"] = ProviderName,
                        ["WhatsAppStatus"] = message?.status ?? "unknown",
                        ["RecipientId"] = message?.recipient_id ?? "unknown"
                    }
                };
            }
            else
            {
                return new MessageStatus
                {
                    MessageId = messageId,
                    Status = "unknown",
                    ErrorMessage = $"Failed to get status: {response.StatusCode}"
                };
            }
        }
        catch (Exception ex)
        {
            return new MessageStatus
            {
                MessageId = messageId,
                Status = "error",
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<MessengerAppScheduleResult> ScheduleMessageAsync(MessengerAppPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default)
    {
        // WhatsApp doesn't support native scheduling
        await Task.Delay(100, cancellationToken);
        
        return new MessengerAppScheduleResult
        {
            ScheduledMessageId = Guid.NewGuid().ToString(),
            IsScheduled = false,
            ErrorMessage = "WhatsApp doesn't support native message scheduling",
            ErrorCode = "NOT_SUPPORTED",
            Platform = "WhatsApp",
            RecipientId = payload.RecipientId,
            MessageType = payload.MessageType,
            ScheduledTime = scheduledTime.DateTime,
            Status = "failed"
        };
    }

    public async Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        return false; // WhatsApp doesn't support scheduling
    }

    public async Task<MessengerAppScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, MessengerAppPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);
        
        return new MessengerAppScheduleResult
        {
            ScheduledMessageId = scheduledMessageId,
            IsScheduled = false,
            ErrorMessage = "WhatsApp doesn't support message scheduling",
            ErrorCode = "NOT_SUPPORTED",
            Status = "failed"
        };
    }

    private object ConvertToWhatsAppPayload(MessengerAppPayload payload)
    {
        var whatsappPayload = new Dictionary<string, object>
        {
            ["messaging_product"] = "whatsapp",
            ["to"] = payload.RecipientId
        };

        switch (payload.MessageType.ToLower())
        {
            case "text":
                whatsappPayload["type"] = "text";
                whatsappPayload["text"] = new { body = payload.Content };
                break;

            case "image":
            case "video":
            case "audio":
            case "document":
                whatsappPayload["type"] = payload.MessageType.ToLower();
                whatsappPayload[payload.MessageType.ToLower()] = new 
                { 
                    link = payload.MediaUrl,
                    caption = payload.Content
                };
                break;

            case "location":
                whatsappPayload["type"] = "location";
                whatsappPayload["location"] = new
                {
                    latitude = payload.Location?.Latitude ?? 0,
                    longitude = payload.Location?.Longitude ?? 0,
                    name = payload.Location?.Name,
                    address = payload.Location?.Address
                };
                break;

            case "contact":
                whatsappPayload["type"] = "contacts";
                whatsappPayload["contacts"] = new[]
                {
                    new
                    {
                        name = new { formatted_name = payload.Contact?.Name },
                        phones = new[] { new { phone = payload.Contact?.PhoneNumber } }
                    }
                };
                break;

            case "template":
                whatsappPayload["type"] = "template";
                whatsappPayload["template"] = new
                {
                    name = payload.TemplateName,
                    language = new { code = payload.TemplateLanguage ?? "en" },
                    components = payload.TemplateParameters?.Select(p => new
                    {
                        type = "body",
                        parameters = new[] { new { type = "text", text = p } }
                    }).ToArray()
                };
                break;

            default:
                whatsappPayload["type"] = "text";
                whatsappPayload["text"] = new { body = payload.Content };
                break;
        }

        return whatsappPayload;
    }

    private string ConvertWhatsAppStatus(string whatsappStatus)
    {
        return whatsappStatus.ToLower() switch
        {
            "sent" => "sent",
            "delivered" => "delivered",
            "read" => "read",
            "failed" => "failed",
            "unknown" => "unknown",
            _ => "unknown"
        };
    }

    private class WhatsAppResponse
    {
        public WhatsAppMessage[]? messages { get; set; }
    }

    private class WhatsAppMessage
    {
        public string? id { get; set; }
    }

    private class WhatsAppErrorResponse
    {
        public WhatsAppError? error { get; set; }
    }

    private class WhatsAppError
    {
        public string? message { get; set; }
        public int code { get; set; }
    }

    private class WhatsAppMessageStatus
    {
        public string? id { get; set; }
        public string? status { get; set; }
        public string? recipient_id { get; set; }
        public DateTime? timestamp { get; set; }
    }

    // ===== ADMIN GATEWAY IMPLEMENTATION =====

    public async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "WhatsApp configuration retrieved successfully",
            Configuration = new Dictionary<string, string>
            {
                ["AccessToken"] = _accessToken.Length > 10 ? $"{_accessToken[..10]}..." : "***",
                ["PhoneNumberId"] = _phoneNumberId,
                ["BusinessAccountId"] = _businessAccountId,
                ["IsEnabled"] = _isInitialized.ToString()
            },
            RequiredFields = new[] { "AccessToken", "PhoneNumberId" },
            OptionalFields = new[] { "BusinessAccountId" }
        };
    }

    public async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
    {
        try
        {
            await InitializeAsync(configuration, cancellationToken);

            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = "WhatsApp configuration updated successfully",
                Configuration = configuration,
                UpdatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "UPDATE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var templates = new[]
        {
            new GatewayTemplate
            {
                Id = "whatsapp-text",
                Name = "Text Message",
                Description = "Simple text message",
                Content = "{{message}}",
                Variables = new Dictionary<string, string> { ["message"] = "string" }
            },
            new GatewayTemplate
            {
                Id = "whatsapp-media",
                Name = "Media Message",
                Description = "Message with image, video, or document",
                Content = "{{caption}} [Media: {{media_url}}]",
                Variables = new Dictionary<string, string> { ["caption"] = "string", ["media_url"] = "url" }
            },
            new GatewayTemplate
            {
                Id = "whatsapp-location",
                Name = "Location Message",
                Description = "Share location coordinates",
                Content = "Location: {{name}} at {{latitude}}, {{longitude}}",
                Variables = new Dictionary<string, string> { ["name"] = "string", ["latitude"] = "number", ["longitude"] = "number" }
            },
            new GatewayTemplate
            {
                Id = "whatsapp-template",
                Name = "Business Template",
                Description = "Pre-approved business message template",
                Content = "Template: {{template_name}} with parameters: {{parameters}}",
                Variables = new Dictionary<string, string> { ["template_name"] = "string", ["parameters"] = "array" }
            }
        };

        return new GatewayTemplatesResult
        {
            IsSuccess = true,
            Templates = templates,
            TotalCount = templates.Length
        };
    }

    public async Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"WhatsApp template '{template.Name}' saved successfully",
            Template = template
        };
    }

    public async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayTemplateResult
        {
            IsSuccess = true,
            Message = $"WhatsApp template '{templateId}' deleted successfully"
        };
    }

    public async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRetryConfiguration
        {
            IsEnabled = true,
            MaxRetries = 2,
            RetryDelaySeconds = new[] { 5, 15 },
            RetryOnErrors = new[] { "RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600
        };
    }

    public async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "WhatsApp retry configuration updated successfully",
            UpdatedAt = DateTime.UtcNow
        };
    }

    public async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            var testMessage = new MessengerAppPayload
            {
                RecipientId = "**********",
                MessageType = "text",
                Content = "This is a test message from WhatsApp gateway"
            };

            var testResult = await SendAsync(testMessage, cancellationToken);

            return new GatewayTestResult
            {
                IsSuccess = testResult.IsSuccess,
                Message = testResult.IsSuccess ? "WhatsApp gateway test successful" : "WhatsApp gateway test failed",
                ResponseTime = DateTime.UtcNow - startTime,
                TestMessageId = testResult.MessageId,
                TestDetails = new Dictionary<string, object>
                {
                    ["Provider"] = ProviderName,
                    ["TestType"] = "LiveTest",
                    ["RecipientId"] = "**********"
                },
                ErrorMessage = testResult.ErrorMessage,
                ErrorCode = testResult.ErrorCode
            };
        }
        catch (Exception ex)
        {
            return new GatewayTestResult
            {
                IsSuccess = false,
                Message = "WhatsApp gateway test failed with exception",
                ResponseTime = DateTime.UtcNow - startTime,
                ErrorMessage = ex.Message,
                ErrorCode = "TEST_EXCEPTION"
            };
        }
    }

    // ===== METRICS GATEWAY IMPLEMENTATION =====

    public async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var days = (endDate - startDate).Days + 1;
        var totalMessages = Random.Shared.Next(300, 3000) * days;

        return new GatewayUsageMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalMessages = totalMessages,
            TotalBulkOperations = totalMessages / 12,
            TotalScheduledMessages = 0, // WhatsApp doesn't support scheduling
            MessagesByDay = GenerateDailyMetrics(startDate, endDate, totalMessages),
            MessagesByHour = GenerateHourlyMetrics(24, totalMessages),
            MessagesByType = new Dictionary<string, long>
            {
                ["text"] = totalMessages * 60 / 100,
                ["image"] = totalMessages * 20 / 100,
                ["video"] = totalMessages * 10 / 100,
                ["document"] = totalMessages * 5 / 100,
                ["template"] = totalMessages * 5 / 100
            },
            AverageMessagesPerDay = totalMessages / (double)days,
            PeakMessagesPerHour = totalMessages / days / 24 * 4.0
        };
    }

    public async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalAttempts = Random.Shared.Next(300, 3000);
        var successfulDeliveries = (long)(totalAttempts * 0.94);

        return new GatewaySuccessRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalAttempts = totalAttempts,
            SuccessfulDeliveries = successfulDeliveries,
            SuccessRate = (double)successfulDeliveries / totalAttempts * 100,
            SuccessRateByDay = GenerateDailySuccessRate(startDate, endDate, 94.0),
            SuccessRateByHour = GenerateHourlySuccessRate(24, 94.0),
            SuccessfulByType = new Dictionary<string, long>
            {
                ["text"] = successfulDeliveries * 60 / 100,
                ["image"] = successfulDeliveries * 20 / 100,
                ["video"] = successfulDeliveries * 10 / 100,
                ["document"] = successfulDeliveries * 5 / 100,
                ["template"] = successfulDeliveries * 5 / 100
            },
            TrendDirection = 0.2
        };
    }

    public async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalFailures = Random.Shared.Next(18, 180);

        return new GatewayFailureRateMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalFailures = totalFailures,
            FailureRate = 6.0,
            FailuresByErrorCode = new Dictionary<string, long>
            {
                ["INVALID_RECIPIENT"] = totalFailures * 35 / 100,
                ["BLOCKED_CONTACT"] = totalFailures * 25 / 100,
                ["RATE_LIMIT"] = totalFailures * 20 / 100,
                ["MEDIA_UPLOAD_FAILED"] = totalFailures * 15 / 100,
                ["TEMPLATE_NOT_APPROVED"] = totalFailures * 5 / 100
            },
            FailuresByDay = GenerateDailyFailures(startDate, endDate, totalFailures),
            TopErrorMessages = new Dictionary<string, string>
            {
                ["INVALID_RECIPIENT"] = "Recipient phone number is not valid or not on WhatsApp",
                ["BLOCKED_CONTACT"] = "Message blocked by recipient or WhatsApp policy",
                ["RATE_LIMIT"] = "Rate limit exceeded for messaging"
            },
            MostCommonErrors = new[] { "INVALID_RECIPIENT", "BLOCKED_CONTACT", "RATE_LIMIT" },
            FailureRateByType = new Dictionary<string, double>
            {
                ["text"] = 4.0,
                ["image"] = 7.0,
                ["video"] = 9.0,
                ["document"] = 6.0,
                ["template"] = 3.0
            }
        };
    }

    public async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayLatencyMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            AverageLatencyMs = 320.8,
            MedianLatencyMs = 280.0,
            P95LatencyMs = 650.0,
            P99LatencyMs = 1200.0,
            MinLatencyMs = 80.0,
            MaxLatencyMs = 2500.0,
            LatencyByDay = GenerateDailyLatency(startDate, endDate, 320.0),
            LatencyByHour = GenerateHourlyLatency(24, 320.0),
            LatencyByType = new Dictionary<string, double>
            {
                ["text"] = 280.0,
                ["image"] = 450.0,
                ["video"] = 680.0,
                ["document"] = 520.0,
                ["template"] = 250.0
            }
        };
    }

    public async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        var totalSent = Random.Shared.Next(300, 3000);
        var totalDelivered = (long)(totalSent * 0.94);
        var totalRead = (long)(totalDelivered * 0.78);

        return new GatewayDeliveryCountMetrics
        {
            StartDate = startDate,
            EndDate = endDate,
            TotalSent = totalSent,
            TotalDelivered = totalDelivered,
            TotalRead = totalRead,
            TotalBounced = totalSent - totalDelivered,
            TotalComplained = (long)(totalDelivered * 0.001),
            DeliveryByDay = GenerateDailyMetrics(startDate, endDate, totalDelivered),
            ReadByDay = GenerateDailyMetrics(startDate, endDate, totalRead),
            BounceByDay = GenerateDailyMetrics(startDate, endDate, totalSent - totalDelivered),
            DeliveryRate = (double)totalDelivered / totalSent * 100,
            ReadRate = (double)totalRead / totalDelivered * 100,
            BounceRate = (double)(totalSent - totalDelivered) / totalSent * 100
        };
    }

    public async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
    {
        var usage = await GetUsageMetricsAsync(startDate, endDate, cancellationToken);
        var successRate = await GetSuccessRateMetricsAsync(startDate, endDate, cancellationToken);
        var failureRate = await GetFailureRateMetricsAsync(startDate, endDate, cancellationToken);
        var latency = await GetLatencyMetricsAsync(startDate, endDate, cancellationToken);
        var deliveryCount = await GetDeliveryCountMetricsAsync(startDate, endDate, cancellationToken);

        return new GatewayAnalyticsDashboard
        {
            StartDate = startDate,
            EndDate = endDate,
            Usage = usage,
            SuccessRate = successRate,
            FailureRate = failureRate,
            Latency = latency,
            DeliveryCount = deliveryCount,
            CustomMetrics = new Dictionary<string, object>
            {
                ["WhatsAppBusinessVerified"] = true,
                ["MessageTemplatesApproved"] = 12,
                ["ConversationRate"] = "78.5%",
                ["MediaUploadSuccessRate"] = "92.3%"
            },
            Insights = new[]
            {
                "Media messages have higher engagement but lower delivery rates",
                "Template messages have the highest success rate",
                "Peak messaging hours are 10 AM - 2 PM and 6 PM - 9 PM"
            },
            Recommendations = new[]
            {
                "Use template messages for important notifications",
                "Optimize media file sizes to improve delivery rates",
                "Implement phone number validation before sending"
            }
        };
    }

    public async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
    {
        await Task.Delay(100, cancellationToken);

        return new GatewayRealTimeMetrics
        {
            Timestamp = DateTime.UtcNow,
            MessagesInLast5Minutes = Random.Shared.Next(1, 30),
            MessagesInLastHour = Random.Shared.Next(30, 300),
            MessagesInLastDay = Random.Shared.Next(300, 3000),
            CurrentSuccessRate = 94.1,
            CurrentLatencyMs = 315.0,
            ActiveConnections = 1,
            QueuedMessages = Random.Shared.Next(0, 10),
            HealthStatus = "healthy",
            LiveStats = new Dictionary<string, object>
            {
                ["WhatsAppAPIStatus"] = "operational",
                ["LastSuccessfulSend"] = DateTime.UtcNow.AddMinutes(-3),
                ["BusinessAccountStatus"] = "verified"
            },
            ActiveAlerts = Array.Empty<string>()
        };
    }

    // Helper methods for generating sample metrics data
    private Dictionary<string, long> GenerateDailyMetrics(DateTime startDate, DateTime endDate, long total)
    {
        var result = new Dictionary<string, long>();
        var days = (endDate - startDate).Days + 1;
        var avgPerDay = total / days;

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.Next(-30, 31);
            var dailyCount = Math.Max(0, avgPerDay + (avgPerDay * variance / 100));
            result[date.ToString("yyyy-MM-dd")] = dailyCount;
        }

        return result;
    }

    private Dictionary<string, long> GenerateHourlyMetrics(int hours, long total)
    {
        var result = new Dictionary<string, long>();
        var avgPerHour = total / hours;

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.Next(-40, 41);
            var hourlyCount = Math.Max(0, avgPerHour + (avgPerHour * variance / 100));
            result[hour.ToString("D2")] = hourlyCount;
        }

        return result;
    }

    private Dictionary<string, double> GenerateDailySuccessRate(DateTime startDate, DateTime endDate, double baseRate)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 8 - 4;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(85, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlySuccessRate(int hours, double baseRate)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 8 - 4;
            result[hour.ToString("D2")] = Math.Max(85, Math.Min(100, baseRate + variance));
        }

        return result;
    }

    private Dictionary<string, long> GenerateDailyFailures(DateTime startDate, DateTime endDate, long totalFailures)
    {
        return GenerateDailyMetrics(startDate, endDate, totalFailures);
    }

    private Dictionary<string, double> GenerateDailyLatency(DateTime startDate, DateTime endDate, double baseLatency)
    {
        var result = new Dictionary<string, double>();

        for (var date = startDate; date <= endDate; date = date.AddDays(1))
        {
            var variance = Random.Shared.NextDouble() * 120 - 60;
            result[date.ToString("yyyy-MM-dd")] = Math.Max(100, baseLatency + variance);
        }

        return result;
    }

    private Dictionary<string, double> GenerateHourlyLatency(int hours, double baseLatency)
    {
        var result = new Dictionary<string, double>();

        for (int hour = 0; hour < hours; hour++)
        {
            var variance = Random.Shared.NextDouble() * 120 - 60;
            result[hour.ToString("D2")] = Math.Max(100, baseLatency + variance);
        }

        return result;
    }
}
