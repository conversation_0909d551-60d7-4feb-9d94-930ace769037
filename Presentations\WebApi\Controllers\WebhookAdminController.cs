#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Webhook administration endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize(Policy = "OnlyAdmins")]
[Produces("application/json")]
public class WebhookAdminController : ControllerBase
{
    /// <summary>
    /// Get webhook service configuration
    /// </summary>
    [HttpGet("config")]
    public async Task<object> Config(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            MaxConcurrentWebhooks = 100,
            DefaultTimeoutSeconds = 30,
            MaxRetries = 3,
            RetryDelaySeconds = 5,
            UseExponentialBackoff = true,
            VerifySslByDefault = true,
            MaxPayloadSizeBytes = 1048576, // 1MB
            AllowedMethods = new[] { "GET", "POST", "PUT", "PATCH", "DELETE" },
            IsEnabled = true,
            RateLimitPerMinute = 1000
        });
    }

    /// <summary>
    /// Get authentication configuration
    /// </summary>
    [HttpGet("auth")]
    public async Task<object> Auth(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            SupportedAuthTypes = new[] { "none", "basic", "bearer", "apikey", "oauth2" },
            DefaultAuthType = "none",
            RequireAuth = false,
            AuthTokenExpiry = 3600,
            RefreshTokens = true
        });
    }

    /// <summary>
    /// Get webhook endpoints configuration
    /// </summary>
    [HttpGet("endpoints")]
    public async Task<object> Endpoints(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            RegisteredEndpoints = new[]
            {
                new { Id = "1", Url = "https://api.example.com/webhook", Status = "Active", LastUsed = DateTime.UtcNow.AddHours(-2) },
                new { Id = "2", Url = "https://hooks.slack.com/services/...", Status = "Active", LastUsed = DateTime.UtcNow.AddMinutes(-15) }
            },
            TotalEndpoints = 2,
            ActiveEndpoints = 2,
            FailedEndpoints = 0
        });
    }

    /// <summary>
    /// Get webhook templates
    /// </summary>
    [HttpGet("templates")]
    public async Task<object> Templates(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Templates = new[]
            {
                new { Id = "user-created", Name = "User Created", Description = "Triggered when a new user is created" },
                new { Id = "order-placed", Name = "Order Placed", Description = "Triggered when an order is placed" },
                new { Id = "payment-received", Name = "Payment Received", Description = "Triggered when payment is received" }
            },
            TotalCount = 3
        });
    }

    /// <summary>
    /// Get retry configuration
    /// </summary>
    [HttpGet("retries")]
    public async Task<object> Retries(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 5, 15, 45 },
            RetryOnStatusCodes = new[] { 408, 429, 500, 502, 503, 504 },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600,
            IsEnabled = true
        });
    }

    /// <summary>
    /// Select webhook provider/gateway
    /// </summary>
    [HttpPost("provider")]
    public async Task<object> Provider([FromBody] SelectWebhookProviderRequest request, CancellationToken cancellationToken)
    {
        var availableProviders = new[] { "Custom HTTP Client", "Zapier Webhooks", "Azure Logic Apps" };

        if (!availableProviders.Contains(request.Provider))
        {
            return BadRequest(new { Error = $"Invalid provider. Available providers: {string.Join(", ", availableProviders)}" });
        }

        return await Task.FromResult(new
        {
            Success = true,
            Message = $"Webhook provider switched to {request.Provider}",
            SelectedProvider = request.Provider,
            AvailableProviders = availableProviders,
            UpdatedAt = DateTime.UtcNow,
            Configuration = request.Provider switch
            {
                "Custom HTTP Client" => new
                {
                    Description = "Direct HTTP client with full customization",
                    Features = new[] { "Custom Headers", "Authentication", "Retry Logic", "Timeout Control" },
                    RequiredSettings = new[] { "TargetUrl", "HttpMethod", "Headers", "Authentication" }
                },
                "Zapier Webhooks" => new
                {
                    Endpoint = "https://hooks.zapier.com/hooks/catch/{webhook_id}/",
                    Features = new[] { "No-code Integration", "1000+ App Connections", "Data Transformation" },
                    RequiredSettings = new[] { "WebhookId", "ZapierApiKey" }
                },
                "Azure Logic Apps" => new
                {
                    Endpoint = "https://{logic-app-name}.azurewebsites.net/api/{trigger-name}",
                    Features = new[] { "Enterprise Integration", "Connectors", "Workflow Automation" },
                    RequiredSettings = new[] { "LogicAppUrl", "SharedAccessSignature", "TriggerName" }
                },
                _ => new { }
            }
        });
    }
}

public class SelectWebhookProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public Dictionary<string, string>? Configuration { get; set; }
}
