#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Email;
using Services.Interfaces;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// Email sending endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class EmailSendController : ControllerBase
{
    private readonly IMessageSender<EmailPayload, EmailResult> _emailSender;
    private readonly IMessageScheduler<EmailPayload, EmailScheduleResult> _emailScheduler;

    public EmailSendController(
        IMessageSender<EmailPayload, EmailResult> emailSender,
        IMessageScheduler<EmailPayload, EmailScheduleResult> emailScheduler)
    {
        _emailSender = emailSender;
        _emailScheduler = emailScheduler;
    }

    /// <summary>
    /// Send a single email
    /// </summary>
    [HttpPost("send")]
    public async Task<ActionResult<EmailResult>> Send([FromBody] EmailPayload payload, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var result = await _emailSender.SendAsync(payload, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Send multiple emails in bulk
    /// </summary>
    [HttpPost("sendbulk")]
    public async Task<ActionResult<IReadOnlyList<EmailResult>>> SendBulk([FromBody] IEnumerable<EmailPayload> payloads, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var results = await _emailSender.SendBulkAsync(payloads, cancellationToken);
        return Ok(results);
    }

    /// <summary>
    /// Schedule an email for future delivery
    /// </summary>
    [HttpPost("schedulesend")]
    public async Task<ActionResult<EmailScheduleResult>> ScheduleSend([FromBody] ScheduleEmailRequest request, CancellationToken cancellationToken)
    {
        if (!ModelState.IsValid) return BadRequest(ModelState);
        var result = await _emailScheduler.ScheduleMessageAsync(request.Payload, request.ScheduledTime, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Get status of a sent email
    /// </summary>
    [HttpGet("status/{messageId}")]
    public async Task<ActionResult<object>> GetStatus(string messageId, CancellationToken cancellationToken)
    {
        var status = await _emailSender.GetStatusAsync(messageId, cancellationToken);
        return Ok(status);
    }

    /// <summary>
    /// Get email analytics
    /// </summary>
    [HttpGet("analytics/{messageId}")]
    public async Task<ActionResult<object>> GetAnalytics(string messageId, CancellationToken cancellationToken)
    {
        return Ok(new
        {
            MessageId = messageId,
            Sent = true,
            Delivered = true,
            Opened = true,
            Clicked = false,
            Bounced = false,
            Complained = false,
            OpenCount = 3,
            ClickCount = 0,
            FirstOpenedAt = DateTime.UtcNow.AddHours(-2),
            LastOpenedAt = DateTime.UtcNow.AddMinutes(-30),
            UserAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            IpAddress = "*************",
            Location = new { Country = "US", Region = "CA", City = "San Francisco" }
        });
    }

    /// <summary>
    /// Resend a previously sent email
    /// </summary>
    [HttpPost("resend/{messageId}")]
    public async Task<ActionResult<EmailResult>> Resend(string messageId, CancellationToken cancellationToken)
    {
        var result = await _emailSender.ResendAsync(messageId, cancellationToken);
        return Ok(result);
    }

    /// <summary>
    /// Preview email content
    /// </summary>
    [HttpPost("preview")]
    public async Task<ActionResult<object>> Preview([FromBody] EmailPayload payload, CancellationToken cancellationToken)
    {
        return Ok(new
        {
            Subject = payload.Subject,
            HtmlPreview = payload.HtmlContent ?? $"<p>{payload.TextContent}</p>",
            TextPreview = payload.TextContent,
            From = payload.From,
            To = payload.To,
            EstimatedSize = (payload.HtmlContent?.Length ?? 0) + (payload.TextContent?.Length ?? 0),
            AttachmentCount = payload.Attachments?.Count ?? 0
        });
    }
}

public class ScheduleEmailRequest
{
    public EmailPayload Payload { get; set; } = new();
    public DateTimeOffset ScheduledTime { get; set; }
}
