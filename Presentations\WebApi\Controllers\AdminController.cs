﻿using AutoMapper;
using Identity.Models;
using Identity.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Models.DTOs.Account;
using Models.DTOs.Email;
using Models.DTOs.SMS;
using Models.DTOs.WhatsApp;
using static Models.DTOs.SMS.SmsConfigurationDto;
using Models.ResponseModels;
using Services.Interfaces;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WebApi.Attributes;

namespace WebApi.Controllers;

/// <summary>
/// Administrative endpoints for user management (requires admin privileges)
/// </summary>
[Authorize]
[Route("api/[controller]")]
[ApiController]
[Produces("application/json")]
public class AdminController : ControllerBase
{
    private readonly IAccountService _accountService;
    private readonly IMapper _mapper;
    private readonly IMessageSender<EmailPayload, EmailResult> _emailSender;
    private readonly IMessageSender<SmsPayload, SmsResult> _smsSender;
    private readonly IMessageService<WhatsAppPayload, WhatsAppResult> _whatsAppService;

    public AdminController(
        IAccountService accountService,
        IMapper mapper,
        IMessageSender<EmailPayload, EmailResult> emailSender,
        IMessageSender<SmsPayload, SmsResult> smsSender,
        IMessageService<WhatsAppPayload, WhatsAppResult> whatsAppService)
    {
        _accountService = accountService;
        _mapper = mapper;
        _emailSender = emailSender;
        _smsSender = smsSender;
        _whatsAppService = whatsAppService;
    }

    /// <summary>
    /// Get all users (Admin only)
    /// </summary>
    /// <returns>List of all users without role information</returns>
    /// <response code="200">Users retrieved successfully</response>
    /// <response code="401">Unauthorized - requires authentication</response>
    /// <response code="403">Forbidden - requires admin role</response>
    [Cached(2)]
    [Authorize(Policy = "OnlyAdmins")]
    [HttpGet("alluser")]
    [ProducesResponseType(typeof(BaseResponse<IReadOnlyList<UserDto>>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<IActionResult> GetAllUser()
    {
        var userList = await _accountService.GetUsers();
        var data = _mapper
            .Map<IReadOnlyList<ApplicationUser>, IReadOnlyList<UserDto>>(userList);

        return Ok(new BaseResponse<IReadOnlyList<UserDto>>(data, $"User List"));
    }

    /// <summary>
    /// Get all users with their roles (SuperAdmin only)
    /// </summary>
    /// <returns>List of all users with their assigned roles</returns>
    /// <response code="200">Users with roles retrieved successfully</response>
    /// <response code="401">Unauthorized - requires authentication</response>
    /// <response code="403">Forbidden - requires SuperAdmin role</response>
    [Cached(1)]
    [Authorize(Roles = "SuperAdmin")]
    [HttpGet("alluserwithroles")]
    [ProducesResponseType(typeof(BaseResponse<IEnumerable<UserDto>>), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<IActionResult> GetAllUserWithRoles()
    {
        var userList = await _accountService.GetUsers();

        var result = userList.Select(x => new UserDto
        {
            Email = x.Email,
            UserName = x.UserName,
            FirstName = x.FirstName,
            LastName = x.LastName,
            Roles = x.UserRoles.ToList().Select(y => y.Role.Name.ToString()).ToList()
        });

        return Ok(new BaseResponse<IEnumerable<UserDto>>(result, $"User List"));
    }

    // ========================================
    // MESSAGING SERVICES ADMIN ENDPOINTS
    // ========================================

    /// <summary>
    /// Check availability of all messaging services
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Service availability status for all messaging channels</returns>
    /// <response code="200">Service availability status retrieved</response>
    /// <response code="401">Unauthorized - requires authentication</response>
    /// <response code="403">Forbidden - requires admin privileges</response>
    [Authorize(Policy = "OnlyAdmins")]
    [HttpGet("messaging/services/availability")]
    [ProducesResponseType(typeof(object), 200)]
    [ProducesResponseType(401)]
    [ProducesResponseType(403)]
    public async Task<object> GetMessagingServicesAvailability(CancellationToken cancellationToken)
    {
        var emailAvailable = await _emailSender.IsAvailableAsync(cancellationToken);
        var smsAvailable = await _smsSender.IsAvailableAsync(cancellationToken);
        var whatsAppAvailable = await _whatsAppService.IsAvailableAsync(cancellationToken);

        return new
        {
            Email = new { Available = emailAvailable, Service = "SendGrid" },
            SMS = new { Available = smsAvailable, Service = "SMS Provider" },
            WhatsApp = new { Available = whatsAppAvailable, Service = "WhatsApp Business API" },
            OverallStatus = emailAvailable && smsAvailable && whatsAppAvailable ? "Healthy" : "Degraded"
        };
    }


}