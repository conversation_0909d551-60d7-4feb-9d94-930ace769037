using Models.DbEntities;
using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Models.DbEntities.Gateway;

[Table("GatewayTemplates")]
public class GatewayTemplate : BaseEntity
{
    [Required]
    [MaxLength(100)]
    public string TemplateId { get; set; } = string.Empty;

    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    [MaxLength(500)]
    public string Description { get; set; } = string.Empty;

    [Required]
    public string Content { get; set; } = string.Empty;

    [MaxLength(50)]
    public string ContentType { get; set; } = "text/plain";

    [Column(TypeName = "jsonb")]
    public string Variables { get; set; } = "{}"; // JSON variables

    [Column(TypeName = "jsonb")]
    public string Tags { get; set; } = "[]"; // JSON array of tags

    public bool IsActive { get; set; } = true;

    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

    [MaxLength(100)]
    public string? CreatedBy { get; set; }

    [MaxLength(100)]
    public string? UpdatedBy { get; set; }

    // Foreign key
    public int GatewayConfigurationId { get; set; }

    // Navigation property
    [ForeignKey("GatewayConfigurationId")]
    public virtual GatewayConfiguration GatewayConfiguration { get; set; } = null!;
}
