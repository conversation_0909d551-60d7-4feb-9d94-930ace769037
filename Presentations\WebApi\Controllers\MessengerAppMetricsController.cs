#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class MessengerAppMetricsController : ControllerBase
{
    [HttpGet("usage")]
    public async Task<object> Usage([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalMessages = 18750,
            UniqueConversations = 3250,
            PlatformBreakdown = new
            {
                WhatsApp = 12500,
                FacebookMessenger = 4200,
                Telegram = 2050
            },
            MessageTypeBreakdown = new
            {
                Text = 14200,
                Image = 2100,
                Document = 1200,
                Audio = 800,
                Video = 450
            },
            ConversationMetrics = new
            {
                NewConversations = 450,
                ActiveConversations = 3250,
                ClosedConversations = 380,
                AverageMessagesPerConversation = 5.8
            }
        });
    }

    [HttpGet("successrate")]
    public async Task<object> SuccessRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallSuccessRate = 98.9,
            TotalSent = 18750,
            TotalDelivered = 18544,
            PlatformSuccessRates = new
            {
                WhatsApp = new { Rate = 99.2, Sent = 12500, Delivered = 12400 },
                FacebookMessenger = new { Rate = 98.8, Sent = 4200, Delivered = 4150 },
                Telegram = new { Rate = 97.6, Sent = 2050, Delivered = 2001 }
            },
            MessageTypeSuccessRates = new
            {
                Text = 99.1,
                Image = 98.5,
                Document = 98.8,
                Audio = 97.9,
                Video = 96.2
            }
        });
    }

    [HttpGet("failurerate")]
    public async Task<object> FailureRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallFailureRate = 1.1,
            TotalFailed = 206,
            FailureReasons = new[]
            {
                new { Reason = "User Blocked Bot", Count = 78, Percentage = 37.9 },
                new { Reason = "Invalid Recipient", Count = 45, Percentage = 21.8 },
                new { Reason = "Rate Limited", Count = 34, Percentage = 16.5 },
                new { Reason = "Media Too Large", Count = 28, Percentage = 13.6 },
                new { Reason = "Network Error", Count = 21, Percentage = 10.2 }
            },
            PlatformFailureRates = new
            {
                WhatsApp = new { Rate = 0.8, Failed = 100 },
                FacebookMessenger = new { Rate = 1.2, Failed = 50 },
                Telegram = new { Rate = 2.4, Failed = 49 }
            }
        });
    }

    [HttpGet("latency")]
    public async Task<object> Latency([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            AverageLatencyMs = 850,
            MedianLatencyMs = 650,
            P95LatencyMs = 2100,
            P99LatencyMs = 4500,
            PlatformLatency = new
            {
                WhatsApp = new { Average = 750, Median = 580, P95 = 1800 },
                FacebookMessenger = new { Average = 920, Median = 720, P95 = 2400 },
                Telegram = new { Average = 1100, Median = 850, P95 = 2800 }
            },
            MessageTypeLatency = new
            {
                Text = 450,
                Image = 1200,
                Document = 1800,
                Audio = 2100,
                Video = 3200
            }
        });
    }

    [HttpGet("deliverycount")]
    public async Task<object> DeliveryCount([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalDelivered = 18544,
            TotalRead = 16890,
            TotalReplied = 8450,
            ReadRate = 91.1,
            ReplyRate = 45.6,
            EngagementMetrics = new
            {
                AverageResponseTime = "2h 15m",
                ConversationCompletionRate = 78.5,
                CustomerSatisfactionScore = 4.2
            },
            DeliveryStages = new
            {
                Sent = 18750,
                Delivered = 18544,
                Read = 16890,
                Replied = 8450,
                Failed = 206
            }
        });
    }
}
