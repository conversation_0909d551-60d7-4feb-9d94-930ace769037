#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Models.DTOs.InApp;

/// <summary>
/// In-app notification payload
/// </summary>
public class InAppPayload
{
    /// <summary>
    /// Target user ID
    /// </summary>
    [Required]
    [StringLength(100)]
    public string UserId { get; set; } = string.Empty;

    /// <summary>
    /// Notification title
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Title { get; set; } = string.Empty;

    /// <summary>
    /// Notification message/content
    /// </summary>
    [Required]
    [StringLength(1000)]
    public string Message { get; set; } = string.Empty;

    /// <summary>
    /// Notification type/category
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Type { get; set; } = "info";

    /// <summary>
    /// Priority level (low, normal, high, urgent)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Optional icon URL or icon name
    /// </summary>
    [StringLength(500)]
    public string? Icon { get; set; }

    /// <summary>
    /// Optional image URL
    /// </summary>
    [StringLength(500)]
    public string? ImageUrl { get; set; }

    /// <summary>
    /// Optional action URL when notification is clicked
    /// </summary>
    [StringLength(500)]
    public string? ActionUrl { get; set; }

    /// <summary>
    /// Action buttons for the notification
    /// </summary>
    public List<InAppActionButton>? ActionButtons { get; set; }

    /// <summary>
    /// Custom data payload
    /// </summary>
    public Dictionary<string, object>? Data { get; set; }

    /// <summary>
    /// Expiration time for the notification
    /// </summary>
    public DateTime? ExpiresAt { get; set; }

    /// <summary>
    /// Whether the notification should be persistent
    /// </summary>
    public bool IsPersistent { get; set; } = true;

    /// <summary>
    /// Whether the notification can be dismissed
    /// </summary>
    public bool IsDismissible { get; set; } = true;

    /// <summary>
    /// Auto-dismiss timeout in seconds
    /// </summary>
    public int? AutoDismissSeconds { get; set; }

    /// <summary>
    /// Tags for categorization and filtering
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Target channels (web, mobile, desktop)
    /// </summary>
    public List<string>? Channels { get; set; }

    /// <summary>
    /// Localization key for multi-language support
    /// </summary>
    [StringLength(100)]
    public string? LocalizationKey { get; set; }

    /// <summary>
    /// Variables for template substitution
    /// </summary>
    public Dictionary<string, string>? TemplateVariables { get; set; }
}

/// <summary>
/// Action button for in-app notifications
/// </summary>
public class InAppActionButton
{
    /// <summary>
    /// Button text
    /// </summary>
    [Required]
    [StringLength(50)]
    public string Text { get; set; } = string.Empty;

    /// <summary>
    /// Action to perform when clicked
    /// </summary>
    [Required]
    [StringLength(200)]
    public string Action { get; set; } = string.Empty;

    /// <summary>
    /// Button style (primary, secondary, danger)
    /// </summary>
    [StringLength(20)]
    public string Style { get; set; } = "secondary";

    /// <summary>
    /// Optional icon for the button
    /// </summary>
    [StringLength(100)]
    public string? Icon { get; set; }
}
