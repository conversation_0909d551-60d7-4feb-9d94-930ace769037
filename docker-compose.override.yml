# =========================
# Docker Compose Override for Development
# This file extends docker-compose.yml for local development
# =========================

version: '3.8'

services:
  # =========================
  # PostgreSQL - Development Overrides
  # =========================
  postgres:
    environment:
      POSTGRES_PASSWORD: devpassword123
    ports:
      - "5432:5432"
    volumes:
      # Add development-specific volumes
      - ./scripts/dev-data:/docker-entrypoint-initdb.d/dev-data
    deploy:
      resources:
        limits:
          memory: 256M
        reservations:
          memory: 128M

  # =========================
  # Redis - Development Overrides
  # =========================
  redis:
    command: redis-server --appendonly yes
    ports:
      - "6379:6379"
    deploy:
      resources:
        limits:
          memory: 128M
        reservations:
          memory: 64M

  # =========================
  # API - Development Overrides
  # =========================
  api:
    build:
      dockerfile: Presentations/WebApi/Dockerfile
      target: base
    environment:
      # Development Environment
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=https://+:443;http://+:80
      - ASPNETCORE_Kestrel__Certificates__Default__Password=password
      - ASPNETCORE_Kestrel__Certificates__Default__Path=/https/aspnetapp.pfx
      
      # Development Database
      - ConnectionStrings__DefaultConnection=Host=postgres;Database=NotifyDb;Username=postgres;Password=devpassword123;Include Error Detail=true;
      - ConnectionStrings__IdentityConnection=Host=postgres;Database=NotifyIdentityDb;Username=postgres;Password=devpassword123;Include Error Detail=true;
      
      # Development JWT (shorter expiration)
      - JWTSettings__Key=development-jwt-key-for-testing-only-32-chars
      - JWTSettings__DurationInMinutes=1440
      
      # Development Email (console output)
      - MailSettings__EmailFrom=<EMAIL>
      - MailSettings__SmtpHost=localhost
      - MailSettings__SmtpPort=1025
      
      # Development Logging
      - Logging__LogLevel__Default=Debug
      - Logging__LogLevel__Microsoft.AspNetCore=Information
      - Logging__LogLevel__Microsoft.EntityFrameworkCore=Information
      
      # Development Features
      - AllowedHosts=*
    ports:
      - "5000:80"
      - "5001:443"
    volumes:
      # Enable hot reload for development
      - ./Presentations/WebApi:/app
      - ./Libraries:/libraries
      - ~/.aspnet/https:/https:ro
    deploy:
      resources:
        limits:
          memory: 512M
        reservations:
          memory: 256M

  # =========================
  # Development Tools
  # =========================
  
  # pgAdmin for database management
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: notify-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "8081:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    depends_on:
      - postgres
    networks:
      - notify-network
    restart: unless-stopped

  # Redis Commander for Redis management
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: notify-redis-commander
    environment:
      REDIS_HOSTS: local:redis:6379
    ports:
      - "8082:8081"
    depends_on:
      - redis
    networks:
      - notify-network
    restart: unless-stopped

  # MailHog for email testing
  mailhog:
    image: mailhog/mailhog:latest
    container_name: notify-mailhog
    ports:
      - "1025:1025"  # SMTP
      - "8025:8025"  # Web UI
    networks:
      - notify-network
    restart: unless-stopped

  # Seq for structured logging
  seq:
    image: datalust/seq:latest
    container_name: notify-seq
    environment:
      ACCEPT_EULA: Y
    ports:
      - "5341:80"
    volumes:
      - seq_data:/data
    networks:
      - notify-network
    restart: unless-stopped

# =========================
# Development Volumes
# =========================
volumes:
  pgadmin_data:
    driver: local
  seq_data:
    driver: local
