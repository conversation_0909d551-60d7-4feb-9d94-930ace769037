using Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Models.DbEntities.Gateway;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Data.Repos.Gateway;

public class GatewayTemplateRepository : GenericRepository<GatewayTemplate>, IGatewayTemplateRepository
{
    private readonly ApplicationDbContext _context;

    public GatewayTemplateRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<List<GatewayTemplate>> GetByGatewayConfigurationIdAsync(int gatewayConfigurationId)
    {
        return await _context.Set<GatewayTemplate>()
            .Where(x => x.GatewayConfigurationId == gatewayConfigurationId)
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public async Task<GatewayTemplate?> GetByTemplateIdAsync(int gatewayConfigurationId, string templateId)
    {
        return await _context.Set<GatewayTemplate>()
            .FirstOrDefaultAsync(x => x.GatewayConfigurationId == gatewayConfigurationId && x.TemplateId == templateId);
    }

    public async Task<List<GatewayTemplate>> GetActiveTemplatesAsync(int gatewayConfigurationId)
    {
        return await _context.Set<GatewayTemplate>()
            .Where(x => x.GatewayConfigurationId == gatewayConfigurationId && x.IsActive)
            .OrderBy(x => x.Name)
            .ToListAsync();
    }

    public async Task<bool> DeleteByTemplateIdAsync(int gatewayConfigurationId, string templateId)
    {
        var template = await GetByTemplateIdAsync(gatewayConfigurationId, templateId);
        if (template == null)
            return false;

        _context.Set<GatewayTemplate>().Remove(template);
        var result = await _context.SaveChangesAsync();
        return result > 0;
    }
}
