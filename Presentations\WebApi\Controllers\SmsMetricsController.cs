#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// SMS metrics and analytics endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize]
[Produces("application/json")]
public class SmsMetricsController : ControllerBase
{
    /// <summary>
    /// Get SMS usage metrics
    /// </summary>
    [HttpGet("usage")]
    public async Task<object> Usage([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalSms = 12450,
            UniqueRecipients = 8750,
            TotalSegments = 13200, // For long messages
            CountryBreakdown = new
            {
                US = 7200,
                UK = 2100,
                CA = 1800,
                AU = 950,
                Other = 400
            },
            MessageTypeBreakdown = new
            {
                Transactional = 8900,
                Marketing = 2100,
                OTP = 1200,
                Alerts = 250
            },
            HourlyDistribution = new[]
            {
                new { Hour = 0, Count = 145 },
                new { Hour = 6, Count = 890 },
                new { Hour = 9, Count = 1250 },
                new { Hour = 12, Count = 1100 },
                new { Hour = 15, Count = 950 },
                new { Hour = 18, Count = 1200 },
                new { Hour = 21, Count = 800 }
            }
        });
    }

    /// <summary>
    /// Get SMS success rate metrics
    /// </summary>
    [HttpGet("successrate")]
    public async Task<object> SuccessRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallSuccessRate = 97.8,
            TotalSent = 12450,
            TotalDelivered = 12176,
            CountrySuccessRates = new
            {
                US = new { Rate = 98.2, Sent = 7200, Delivered = 7070 },
                UK = new { Rate = 97.9, Sent = 2100, Delivered = 2056 },
                CA = new { Rate = 98.1, Sent = 1800, Delivered = 1766 },
                AU = new { Rate = 96.8, Sent = 950, Delivered = 920 },
                Other = new { Rate = 91.0, Sent = 400, Delivered = 364 }
            },
            CarrierSuccessRates = new
            {
                Verizon = 98.5,
                ATT = 98.1,
                TMobile = 97.9,
                Sprint = 97.2,
                Other = 96.8
            },
            TrendData = new[]
            {
                new { Date = DateTime.UtcNow.AddDays(-6).ToString("yyyy-MM-dd"), Rate = 97.2 },
                new { Date = DateTime.UtcNow.AddDays(-5).ToString("yyyy-MM-dd"), Rate = 97.8 },
                new { Date = DateTime.UtcNow.AddDays(-4).ToString("yyyy-MM-dd"), Rate = 98.1 },
                new { Date = DateTime.UtcNow.AddDays(-3).ToString("yyyy-MM-dd"), Rate = 97.5 },
                new { Date = DateTime.UtcNow.AddDays(-2).ToString("yyyy-MM-dd"), Rate = 97.9 },
                new { Date = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd"), Rate = 98.0 },
                new { Date = DateTime.UtcNow.ToString("yyyy-MM-dd"), Rate = 97.8 }
            }
        });
    }

    /// <summary>
    /// Get SMS failure rate metrics
    /// </summary>
    [HttpGet("failurerate")]
    public async Task<object> FailureRate([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            OverallFailureRate = 2.2,
            TotalFailed = 274,
            FailureReasons = new[]
            {
                new { Reason = "Invalid Number", Count = 98, Percentage = 35.8 },
                new { Reason = "Carrier Blocked", Count = 67, Percentage = 24.5 },
                new { Reason = "Network Error", Count = 45, Percentage = 16.4 },
                new { Reason = "Rate Limited", Count = 34, Percentage = 12.4 },
                new { Reason = "Content Filtered", Count = 20, Percentage = 7.3 },
                new { Reason = "Other", Count = 10, Percentage = 3.6 }
            },
            CountryFailureRates = new
            {
                US = new { Rate = 1.8, Failed = 130 },
                UK = new { Rate = 2.1, Failed = 44 },
                CA = new { Rate = 1.9, Failed = 34 },
                AU = new { Rate = 3.2, Failed = 30 },
                Other = new { Rate = 9.0, Failed = 36 }
            }
        });
    }

    /// <summary>
    /// Get SMS latency metrics
    /// </summary>
    [HttpGet("latency")]
    public async Task<object> Latency([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            AverageLatencyMs = 1850,
            MedianLatencyMs = 1200,
            P95LatencyMs = 4500,
            P99LatencyMs = 8900,
            MinLatencyMs = 250,
            MaxLatencyMs = 45000,
            CountryLatency = new
            {
                US = new { Average = 1200, Median = 950, P95 = 3200 },
                UK = new { Average = 1800, Median = 1400, P95 = 4100 },
                CA = new { Average = 1650, Median = 1300, P95 = 3800 },
                AU = new { Average = 2800, Median = 2200, P95 = 6500 },
                Other = new { Average = 4200, Median = 3500, P95 = 9800 }
            },
            CarrierLatency = new
            {
                Verizon = 1100,
                ATT = 1250,
                TMobile = 1400,
                Sprint = 1650,
                International = 3200
            }
        });
    }

    /// <summary>
    /// Get SMS delivery count metrics
    /// </summary>
    [HttpGet("deliverycount")]
    public async Task<object> DeliveryCount([FromQuery] string period = "day", CancellationToken cancellationToken = default)
    {
        return await Task.FromResult(new
        {
            Period = period,
            TotalDelivered = 12176,
            TotalPending = 89,
            TotalFailed = 274,
            DeliveryRate = 97.8,
            DeliveryStages = new
            {
                Queued = 45,
                Sent = 12450,
                Delivered = 12176,
                Failed = 274,
                Pending = 89
            },
            MessageCosts = new
            {
                TotalCost = 124.50m,
                AverageCostPerMessage = 0.01m,
                CostByCountry = new
                {
                    US = 72.00m,
                    UK = 21.00m,
                    CA = 18.00m,
                    AU = 9.50m,
                    Other = 4.00m
                }
            },
            DeliveryTrend = new[]
            {
                new { Date = DateTime.UtcNow.AddDays(-6).ToString("yyyy-MM-dd"), Delivered = 11245 },
                new { Date = DateTime.UtcNow.AddDays(-5).ToString("yyyy-MM-dd"), Delivered = 13890 },
                new { Date = DateTime.UtcNow.AddDays(-4).ToString("yyyy-MM-dd"), Delivered = 14678 },
                new { Date = DateTime.UtcNow.AddDays(-3).ToString("yyyy-MM-dd"), Delivered = 12234 },
                new { Date = DateTime.UtcNow.AddDays(-2).ToString("yyyy-MM-dd"), Delivered = 11567 },
                new { Date = DateTime.UtcNow.AddDays(-1).ToString("yyyy-MM-dd"), Delivered = 13123 },
                new { Date = DateTime.UtcNow.ToString("yyyy-MM-dd"), Delivered = 12176 }
            }
        });
    }
}
