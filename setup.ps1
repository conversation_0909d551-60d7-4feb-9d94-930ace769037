# File: setup.ps1
# Main script to automate the setup of the TUI program

function Show-Menu {
    param (
        [string]$Title,
        [string[]]$Options
    )

    Write-Host "================ $($Title) ================" -ForegroundColor Cyan

    for ($i = 0; $i -lt $Options.Length; $i++) {
        Write-Host "[$($i+1)] $($Options[$i])" -ForegroundColor Yellow
    }

    Write-Host "==========================================" -ForegroundColor Cyan
}

# Function to check if a program is installed
function Is-Installed {
    param (
        [string]$ProgramName
    )
    try {
        # Use Get-Command for a more robust check for executables in PATH
        if (Get-Command $ProgramName -ErrorAction SilentlyContinue) {
            # Attempt to get version if the command exists and supports --version
            if ($ProgramName -eq "dotnet" -or $ProgramName -eq "node" -or $ProgramName -eq "docker") {
                $version = & $ProgramName --version 2>&1
                return $version.Trim()
            }
            return $true # Program is found, but no specific version check
        }
        return $false
    }
    catch {
        return $false
    }
}

# Function to install Chocolatey if not present
function Install-Chocolatey {
    if (-not (Is-Installed "choco")) {
        Write-Host "Chocolatey is not installed. Installing Chocolatey..." -ForegroundColor Yellow
        try {
            Set-ExecutionPolicy Bypass -Scope Process -Force
            [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor [System.Net.SecurityProtocolType]::Tls12
            iex ((New-Object System.Net.WebClient).DownloadString('https://chocolatey.org/install.ps1'))
            Write-Host "Chocolatey installed successfully." -ForegroundColor Green
        }
        catch {
            Write-Error "Failed to install Chocolatey: $($_.Exception.Message)"
            exit 1
        }
    }
    else {
        Write-Host "Chocolatey is already installed." -ForegroundColor Green
    }
}

# Step 0: Ensure Chocolatey is installed
Install-Chocolatey

# Step 1: Install Dependencies
$options = @(
    "Install .NET SDK",
    "Install Node.js",
    "Install Docker Desktop",
    "Download Application",
    "Configure Application",
    "Run Application",
    "Exit"
)

do {
    Write-Host "Checking installed programs..." -ForegroundColor Green

    $dotnetVersion = Is-Installed "dotnet"
    $nodeVersion = Is-Installed "node"
    $dockerVersion = Is-Installed "docker"

    Write-Host ".NET SDK: $($dotnetVersion)" -ForegroundColor Green
    Write-Host "Node.js: $($nodeVersion)" -ForegroundColor Green
    Write-Host "Docker Desktop: $($dockerVersion)" -ForegroundColor Green

    Show-Menu -Title "TUI Setup Menu" -Options $options

    $selection = Read-Host "Enter your choice"

    switch ($selection) {
        "1" {
            Write-Host "Installing .NET SDK" -ForegroundColor Green
            # Use PSProgress to show progress bar
            $progressParams = @{
                Activity        = "Installing .NET SDK"
                Status          = "Starting..."
                PercentComplete = 0
            }
            Write-Progress @progressParams

            # Install .NET SDK
            # Consider making this version configurable or fetching the latest stable version
            $dotnetTargetVersion = "5.0"
            try {
                Write-Host "Attempting to install .NET SDK version $dotnetTargetVersion..." -ForegroundColor Yellow
                choco install dotnet-sdk."$dotnetTargetVersion" -y --no-progress
                Write-Host ".NET SDK installed successfully." -ForegroundColor Green
            }
            catch {
                Write-Error "Failed to install .NET SDK: $($_.Exception.Message)"
            }

            $progressParams = @{
                Activity        = "Installing .NET SDK"
                Status          = "Completed"
                PercentComplete = 100
            }
            Write-Progress @progressParams
        }
        "2" {
            Write-Host "Installing Node.js" -ForegroundColor Green
            # Use PSProgress to show progress bar
            $progressParams = @{
                Activity        = "Installing Node.js"
                Status          = "Starting..."
                PercentComplete = 0
            }
            Write-Progress @progressParams

            # Install Node.js
            # Consider making this version configurable or fetching the latest stable version
            $nodeTargetVersion = "16"
            try {
                Write-Host "Attempting to install Node.js version $nodeTargetVersion..." -ForegroundColor Yellow
                choco install nodejs."$nodeTargetVersion" -y --no-progress
                Write-Host "Node.js installed successfully." -ForegroundColor Green
            }
            catch {
                Write-Error "Failed to install Node.js: $($_.Exception.Message)"
            }

            $progressParams = @{
                Activity        = "Installing Node.js"
                Status          = "Completed"
                PercentComplete = 100
            }
            Write-Progress @progressParams
        }
        "3" {
            Write-Host "Installing Docker Desktop" -ForegroundColor Green
            # Use PSProgress to show progress bar
            $progressParams = @{
                Activity        = "Installing Docker Desktop"
                Status          = "Starting..."
                PercentComplete = 0
            }
            Write-Progress @progressParams

            # Install Docker Desktop
            try {
                Write-Host "Attempting to install Docker Desktop..." -ForegroundColor Yellow
                choco install docker-desktop -y --no-progress
                Write-Host "Docker Desktop installed successfully." -ForegroundColor Green
            }
            catch {
                Write-Error "Failed to install Docker Desktop: $($_.Exception.Message)"
            }

            $progressParams = @{
                Activity        = "Installing Docker Desktop"
                Status          = "Completed"
                PercentComplete = 100
            }
            Write-Progress @progressParams
        }
        "4" {
            Write-Host "Downloading Application" -ForegroundColor Green
            # Assuming download-application.ps1 exists and handles its own errors
            .\download-application.ps1
        }
        "5" {
            Write-Host "Configuring Application" -ForegroundColor Green
            # Assuming configure-application.ps1 exists and handles its own errors
            .\configure-application.ps1
        }
        "6" {
            Write-Host "Running Application" -ForegroundColor Green
            # Assuming run-application.ps1 exists and handles its own errors
            .\run-application.ps1
        }
        "7" {
            Write-Host "Exiting" -ForegroundColor Green
            break
        }
        default {
            Write-Host "Invalid selection. Please try again." -ForegroundColor Red
        }
    }
} while ($selection -ne "7")

Write-Host "Setup completed." -ForegroundColor Cyan
        }
    }
} while ($selection -ne "7")

Write-Host "Setup completed." -ForegroundColor Cyan
