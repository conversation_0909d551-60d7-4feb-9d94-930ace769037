using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading;
using System.Threading.Tasks;
using Models.DTOs.MessengerApp;
using Models.DTOs.Gateway;
using Services.Interfaces;
using Services.Gateway;

namespace Services.Gateways.Base
{
    public abstract class BaseMessengerAppGateway : BaseGateway, IMessengerAppGateway, IAdminGateway, IMetricsGateway
    {
        protected BaseMessengerAppGateway(HttpClient httpClient, IGatewayDataService dataService, string gatewayName)
            : base(httpClient, dataService, gatewayName)
        {
        }

        // IGateway interface implementations
        public abstract string ProviderName { get; }
        public virtual bool IsEnabled => true;
        public virtual Task InitializeAsync(Dictionary<string, string> configuration, CancellationToken cancellationToken = default)
        {
            return Task.CompletedTask;
        }

        // IMessengerAppGateway interface implementations
        public virtual Task<MessengerAppResult> SendWithTemplateAsync(string templateId, object templateData, string recipientId, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Template sending not implemented for this MessengerApp gateway");
        }

        public virtual Task<IReadOnlyList<MessengerAppResult>> SendBulkWithTemplateAsync(string templateId, IEnumerable<(object templateData, string recipientId)> recipients, CancellationToken cancellationToken = default)
        {
            throw new NotImplementedException("Bulk template sending not implemented for this MessengerApp gateway");
        }

        // Abstract methods that must be implemented by derived classes
        public abstract Task<MessengerAppResult> SendAsync(MessengerAppPayload payload, CancellationToken cancellationToken = default);
        public abstract Task<IReadOnlyList<MessengerAppResult>> SendBulkAsync(IEnumerable<MessengerAppPayload> payloads, CancellationToken cancellationToken = default);
        public abstract Task<MessageStatus> GetStatusAsync(string messageId, CancellationToken cancellationToken = default);
        public abstract Task<MessengerAppScheduleResult> ScheduleMessageAsync(MessengerAppPayload payload, DateTimeOffset scheduledTime, CancellationToken cancellationToken = default);
        public abstract Task<bool> CancelScheduledMessageAsync(string scheduledMessageId, CancellationToken cancellationToken = default);
        public abstract Task<MessengerAppScheduleResult> UpdateScheduledMessageAsync(string scheduledMessageId, MessengerAppPayload newPayload, DateTimeOffset? newScheduledTime = null, CancellationToken cancellationToken = default);
        public abstract Task<GatewayHealthResult> HealthCheckAsync(CancellationToken cancellationToken = default);
        public abstract GatewayCapabilities GetCapabilities();

        // Helper methods for creating standardized results
        protected MessengerAppResult CreateSuccessResult(MessengerAppPayload payload, string messageId, string responseContent = null)
        {
            return new MessengerAppResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = true,
                SentAt = DateTime.UtcNow
            };
        }

        protected MessengerAppResult CreateErrorResult(MessengerAppPayload payload, string errorMessage, string messageId = null)
        {
            return new MessengerAppResult
            {
                MessageId = messageId ?? Guid.NewGuid().ToString(),
                IsSuccess = false,
                ErrorMessage = errorMessage,
                SentAt = DateTime.UtcNow
            };
        }

        protected MessengerAppScheduleResult CreateSuccessScheduleResult(MessengerAppPayload payload, string scheduledMessageId, DateTimeOffset scheduledTime, string responseContent = null)
        {
            return new MessengerAppScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = true,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        protected MessengerAppScheduleResult CreateErrorScheduleResult(MessengerAppPayload payload, DateTimeOffset scheduledTime, string errorMessage, string scheduledMessageId = null)
        {
            return new MessengerAppScheduleResult
            {
                ScheduledMessageId = scheduledMessageId ?? Guid.NewGuid().ToString(),
                IsScheduled = false,
                ErrorMessage = errorMessage,
                ScheduledTime = scheduledTime.DateTime
            };
        }

        // Default implementations for IAdminGateway
        public virtual async Task<GatewayTestResult> TestConnectionAsync(CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var testMessage = new MessengerAppPayload
                {
                    Platform = _gatewayName,
                    RecipientId = "test_recipient",
                    MessageType = "text",
                    Text = $"Test message from {_gatewayName} gateway"
                };
                
                var testResult = await SendAsync(testMessage, cancellationToken);
                return new GatewayTestResult
                {
                    IsSuccess = testResult.IsSuccess,
                    Message = testResult.IsSuccess ? $"{_gatewayName} gateway test successful" : $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    TestMessageId = testResult.MessageId,
                    TestDetails = new Dictionary<string, object> { ["Provider"] = _gatewayName, ["TestType"] = "LiveTest", ["RecipientId"] = "test_recipient" },
                    ErrorMessage = testResult.ErrorMessage
                };
            }
            catch (Exception ex)
            {
                return new GatewayTestResult
                {
                    IsSuccess = false,
                    Message = $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    ErrorMessage = ex.Message,
                    TestDetails = new Dictionary<string, object> { ["Provider"] = _gatewayName, ["TestType"] = "LiveTest", ["Error"] = ex.Message }
                };
            }
        }

        public virtual async Task<GatewayConfigurationResult> UpdateConfigurationAsync(Dictionary<string, string> config, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveGatewayConfigurationAsync(_gatewayName, config, cancellationToken);
                return new GatewayConfigurationResult
                {
                    IsSuccess = true,
                    Message = $"{_gatewayName} configuration updated successfully",
                    Configuration = config,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
            catch (Exception ex)
            {
                return new GatewayConfigurationResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayConfigurationResult> GetConfigurationAsync(CancellationToken cancellationToken = default)
        {
            var config = await _dataService.GetGatewayConfigurationAsync(_gatewayName, cancellationToken);
            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Configuration = config,
                GatewayName = _gatewayName,
                Provider = ProviderName
            };
        }

        public virtual async Task<GatewayTemplateResult> CreateTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                var templateId = await _dataService.CreateTemplateAsync(_gatewayName, template, cancellationToken);
                return new GatewayTemplateResult
                {
                    IsSuccess = true,
                    TemplateId = templateId,
                    Message = "Template created successfully"
                };
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public virtual async Task<GatewayTemplateResult> UpdateTemplateAsync(string templateId, GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.UpdateTemplateAsync(_gatewayName, templateId, template, cancellationToken);
                return new GatewayTemplateResult
                {
                    IsSuccess = true,
                    TemplateId = templateId,
                    Message = "Template updated successfully"
                };
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public virtual async Task<GatewayTemplateResult> DeleteTemplateAsync(string templateId, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _dataService.DeleteTemplateResultAsync(_gatewayName, ProviderName, templateId, cancellationToken);
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    TemplateId = templateId,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayTemplatesResult> GetTemplatesAsync(CancellationToken cancellationToken = default)
        {
            try
            {
                return await _dataService.GetTemplatesResultAsync(_gatewayName, ProviderName, cancellationToken);
            }
            catch (Exception ex)
            {
                return new GatewayTemplatesResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayTemplateResult> SaveTemplateAsync(GatewayTemplate template, CancellationToken cancellationToken = default)
        {
            try
            {
                return await _dataService.SaveTemplateResultAsync(_gatewayName, ProviderName, template, cancellationToken);
            }
            catch (Exception ex)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    TemplateId = template?.TemplateId,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        public virtual async Task<GatewayRetryPolicyResult> SetRetryPolicyAsync(GatewayRetryPolicy policy, CancellationToken cancellationToken = default)
        {
            try
            {
                await _dataService.SaveRetryPolicyAsync(_gatewayName, policy, cancellationToken);
                return new GatewayRetryPolicyResult
                {
                    IsSuccess = true,
                    Message = "Retry policy updated successfully"
                };
            }
            catch (Exception ex)
            {
                return new GatewayRetryPolicyResult
                {
                    IsSuccess = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        public virtual async Task<GatewayRetryPolicy> GetRetryPolicyAsync(CancellationToken cancellationToken = default)
        {
            return await _dataService.GetRetryPolicyAsync(_gatewayName, cancellationToken);
        }

        public virtual async Task<GatewayRetryConfiguration> GetRetryConfigurationAsync(CancellationToken cancellationToken = default)
        {
            await Task.Delay(100, cancellationToken);
            return new GatewayRetryConfiguration
            {
                IsEnabled = true,
                MaxRetries = 3,
                RetryDelaySeconds = new[] { 5, 15, 45 },
                RetryOnErrors = new[] { "RATE_LIMIT", "TIMEOUT", "NETWORK_ERROR", "TEMPORARY_FAILURE" },
                ExponentialBackoff = true,
                MaxRetryDelay = 300,
                TotalRetryTimeout = 600
            };
        }

        public virtual async Task<GatewayConfigurationResult> UpdateRetryConfigurationAsync(GatewayRetryConfiguration retryConfig, CancellationToken cancellationToken = default)
        {
            await Task.Delay(100, cancellationToken);
            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = $"{_gatewayName} retry configuration updated successfully",
                GatewayName = _gatewayName,
                Provider = ProviderName,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public virtual async Task<GatewayTestResult> TestGatewayAsync(object testPayload, CancellationToken cancellationToken = default)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                var testMessage = new MessengerAppPayload
                {
                    Platform = _gatewayName,
                    RecipientId = "test_recipient",
                    MessageType = "text",
                    Text = $"Test message from {_gatewayName} gateway"
                };

                var testResult = await SendAsync(testMessage, cancellationToken);
                return new GatewayTestResult
                {
                    IsSuccess = testResult.IsSuccess,
                    Message = testResult.IsSuccess ? $"{_gatewayName} gateway test successful" : $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    TestMessageId = testResult.MessageId,
                    TestDetails = new Dictionary<string, object>
                    {
                        ["Provider"] = ProviderName,
                        ["TestType"] = "LiveTest",
                        ["RecipientId"] = "test_recipient"
                    },
                    ErrorMessage = testResult.ErrorMessage,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
            catch (Exception ex)
            {
                return new GatewayTestResult
                {
                    IsSuccess = false,
                    Message = $"{_gatewayName} gateway test failed",
                    ResponseTime = DateTime.UtcNow - startTime,
                    ErrorMessage = ex.Message,
                    GatewayName = _gatewayName,
                    Provider = ProviderName
                };
            }
        }

        // Default implementations for IMetricsGateway
        public virtual async Task<GatewayUsageMetrics> GetUsageMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetUsageMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayPerformanceMetrics> GetPerformanceMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetPerformanceMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewaySuccessRateMetrics> GetSuccessRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetSuccessRateMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayFailureRateMetrics> GetFailureRateMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetFailureRateMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayLatencyMetrics> GetLatencyMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetLatencyMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayDeliveryCountMetrics> GetDeliveryCountMetricsAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetDeliveryCountMetricsAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayAnalyticsDashboard> GetAnalyticsDashboardAsync(DateTime startDate, DateTime endDate, CancellationToken cancellationToken = default)
        {
            return await _dataService.GetAnalyticsDashboardAsync(_gatewayName, startDate, endDate, cancellationToken);
        }

        public virtual async Task<GatewayRealTimeMetrics> GetRealTimeMetricsAsync(CancellationToken cancellationToken = default)
        {
            return await _dataService.GetRealTimeMetricsAsync(_gatewayName, cancellationToken);
        }

        public virtual async Task LogMessageAsync(string messageId, string status, Dictionary<string, object> metadata = null, CancellationToken cancellationToken = default)
        {
            await _dataService.LogMessageAsync(_gatewayName, messageId, status, metadata, cancellationToken);
        }

        public virtual async Task RecordMetricAsync(string metricName, double value, Dictionary<string, string> tags = null, CancellationToken cancellationToken = default)
        {
            await _dataService.RecordMetricAsync(_gatewayName, metricName, value, tags, cancellationToken);
        }
    }
}
