﻿using System;
using Caching;
using GraphQL.Types;
using Services.Interfaces;
using WebApi.GraphQL.Types.Note;

namespace WebApi.GraphQL.Queries;

public class MyNoteQuery : ObjectGraphType
{
    [Obsolete("Obsolete")]
    public MyNoteQuery(INoteService noteService, ICacheManager cacheManager, IAuthenticatedUserService authenticatedUserService)
    {

        Field<NoteType>(
            "note_by_id",
            arguments: new QueryArguments(new QueryArgument<IntGraphType> { Name = "id" }),
            resolve: context =>
            {
                // int noteId = context.GetArgument<int>("id");
                return noteService.GetNoteById(10);
            });

    }
}