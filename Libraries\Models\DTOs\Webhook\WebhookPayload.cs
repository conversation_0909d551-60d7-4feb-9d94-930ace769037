#nullable enable
using System;
using System.ComponentModel.DataAnnotations;
using System.Collections.Generic;

namespace Models.DTOs.Webhook;

/// <summary>
/// Webhook payload
/// </summary>
public class WebhookPayload
{
    /// <summary>
    /// Target webhook URL
    /// </summary>
    [Required]
    [Url]
    [StringLength(2000)]
    public string Url { get; set; } = string.Empty;

    /// <summary>
    /// HTTP method (GET, POST, PUT, PATCH, DELETE)
    /// </summary>
    [Required]
    [StringLength(10)]
    public string Method { get; set; } = "POST";

    /// <summary>
    /// Request headers
    /// </summary>
    public Dictionary<string, string>? Headers { get; set; }

    /// <summary>
    /// Request body/payload
    /// </summary>
    public object? Body { get; set; }

    /// <summary>
    /// Content type for the request
    /// </summary>
    [StringLength(100)]
    public string ContentType { get; set; } = "application/json";

    /// <summary>
    /// Request timeout in seconds
    /// </summary>
    [Range(1, 300)]
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Maximum number of retry attempts
    /// </summary>
    [Range(0, 10)]
    public int MaxRetries { get; set; } = 3;

    /// <summary>
    /// Retry delay in seconds
    /// </summary>
    [Range(1, 3600)]
    public int RetryDelaySeconds { get; set; } = 5;

    /// <summary>
    /// Whether to use exponential backoff for retries
    /// </summary>
    public bool UseExponentialBackoff { get; set; } = true;

    /// <summary>
    /// Authentication type (none, basic, bearer, apikey)
    /// </summary>
    [StringLength(20)]
    public string AuthType { get; set; } = "none";

    /// <summary>
    /// Authentication credentials
    /// </summary>
    public Dictionary<string, string>? AuthCredentials { get; set; }

    /// <summary>
    /// Event type or category
    /// </summary>
    [StringLength(100)]
    public string? EventType { get; set; }

    /// <summary>
    /// Unique event identifier
    /// </summary>
    [StringLength(100)]
    public string? EventId { get; set; }

    /// <summary>
    /// Event timestamp
    /// </summary>
    public DateTime? EventTimestamp { get; set; }

    /// <summary>
    /// Custom metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Whether to verify SSL certificates
    /// </summary>
    public bool VerifySsl { get; set; } = true;

    /// <summary>
    /// Whether to follow redirects
    /// </summary>
    public bool FollowRedirects { get; set; } = true;

    /// <summary>
    /// Maximum number of redirects to follow
    /// </summary>
    [Range(0, 10)]
    public int MaxRedirects { get; set; } = 5;

    /// <summary>
    /// User agent string
    /// </summary>
    [StringLength(200)]
    public string? UserAgent { get; set; }

    /// <summary>
    /// Expected HTTP status codes for success
    /// </summary>
    public int[]? ExpectedStatusCodes { get; set; }

    /// <summary>
    /// Whether to include response body in result
    /// </summary>
    public bool IncludeResponseBody { get; set; } = false;

    /// <summary>
    /// Priority level (low, normal, high, critical)
    /// </summary>
    [StringLength(20)]
    public string Priority { get; set; } = "normal";
}
