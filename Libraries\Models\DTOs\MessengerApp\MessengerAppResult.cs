#nullable enable
using System;
using System.Collections.Generic;

namespace Models.DTOs.MessengerApp;

/// <summary>
/// Messenger app message send result
/// </summary>
public class MessengerAppResult
{
    /// <summary>
    /// Unique message identifier
    /// </summary>
    public string MessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the message was sent successfully
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// Error message if sending failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if sending failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Target platform
    /// </summary>
    public string? Platform { get; set; }

    /// <summary>
    /// Recipient identifier
    /// </summary>
    public string? RecipientId { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public string? MessageType { get; set; }

    /// <summary>
    /// Timestamp when the message was sent
    /// </summary>
    public DateTime SentAt { get; set; }

    /// <summary>
    /// Message status (sent, delivered, read, failed)
    /// </summary>
    public string Status { get; set; } = "sent";

    /// <summary>
    /// Conversation ID
    /// </summary>
    public string? ConversationId { get; set; }

    /// <summary>
    /// Platform-specific message ID
    /// </summary>
    public string? PlatformMessageId { get; set; }

    /// <summary>
    /// Message priority
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Delivery tracking information
    /// </summary>
    public MessengerDeliveryTracking? DeliveryTracking { get; set; }

    /// <summary>
    /// Platform-specific response data
    /// </summary>
    public Dictionary<string, object>? PlatformResponse { get; set; }

    /// <summary>
    /// Additional metadata
    /// </summary>
    public Dictionary<string, object>? Metadata { get; set; }

    /// <summary>
    /// Tags associated with the message
    /// </summary>
    public List<string>? Tags { get; set; }

    /// <summary>
    /// Processing time in milliseconds
    /// </summary>
    public long? ProcessingTimeMs { get; set; }

    /// <summary>
    /// Retry information if applicable
    /// </summary>
    public MessengerRetryInfo? RetryInfo { get; set; }
}

/// <summary>
/// Delivery tracking information
/// </summary>
public class MessengerDeliveryTracking
{
    /// <summary>
    /// Delivery receipt requested
    /// </summary>
    public bool DeliveryReceiptRequested { get; set; }

    /// <summary>
    /// Read receipt requested
    /// </summary>
    public bool ReadReceiptRequested { get; set; }

    /// <summary>
    /// Delivery timestamp
    /// </summary>
    public DateTime? DeliveredAt { get; set; }

    /// <summary>
    /// Read timestamp
    /// </summary>
    public DateTime? ReadAt { get; set; }

    /// <summary>
    /// Delivery status details
    /// </summary>
    public string? DeliveryStatus { get; set; }

    /// <summary>
    /// Tracking URL for detailed status
    /// </summary>
    public string? TrackingUrl { get; set; }
}

/// <summary>
/// Retry information
/// </summary>
public class MessengerRetryInfo
{
    /// <summary>
    /// Number of retry attempts made
    /// </summary>
    public int AttemptCount { get; set; }

    /// <summary>
    /// Maximum retry attempts allowed
    /// </summary>
    public int MaxAttempts { get; set; }

    /// <summary>
    /// Next retry time if applicable
    /// </summary>
    public DateTime? NextRetryAt { get; set; }

    /// <summary>
    /// Retry reason
    /// </summary>
    public string? RetryReason { get; set; }

    /// <summary>
    /// Whether retries are exhausted
    /// </summary>
    public bool RetriesExhausted { get; set; }
}

/// <summary>
/// Messenger app message schedule result
/// </summary>
public class MessengerAppScheduleResult
{
    /// <summary>
    /// Unique scheduled message identifier
    /// </summary>
    public string ScheduledMessageId { get; set; } = string.Empty;

    /// <summary>
    /// Whether the scheduling was successful
    /// </summary>
    public bool IsScheduled { get; set; }

    /// <summary>
    /// Scheduled delivery time
    /// </summary>
    public DateTime ScheduledTime { get; set; }

    /// <summary>
    /// Error message if scheduling failed
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// Error code if scheduling failed
    /// </summary>
    public string? ErrorCode { get; set; }

    /// <summary>
    /// Target platform
    /// </summary>
    public string? Platform { get; set; }

    /// <summary>
    /// Recipient identifier
    /// </summary>
    public string? RecipientId { get; set; }

    /// <summary>
    /// Message type
    /// </summary>
    public string? MessageType { get; set; }

    /// <summary>
    /// Timestamp when the scheduling was created
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Message priority
    /// </summary>
    public string Priority { get; set; } = "normal";

    /// <summary>
    /// Whether the scheduled message can be cancelled
    /// </summary>
    public bool CanCancel { get; set; } = true;

    /// <summary>
    /// Whether the scheduled message can be modified
    /// </summary>
    public bool CanModify { get; set; } = true;

    /// <summary>
    /// Schedule status (scheduled, cancelled, sent, failed)
    /// </summary>
    public string Status { get; set; } = "scheduled";

    /// <summary>
    /// Time zone for scheduled delivery
    /// </summary>
    public string? TimeZone { get; set; }

    /// <summary>
    /// Additional scheduling metadata
    /// </summary>
    public Dictionary<string, object>? SchedulingMetadata { get; set; }
}
