using Data.Contexts;
using Microsoft.EntityFrameworkCore;
using Models.DbEntities.Gateway;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Data.Repos.Gateway;

public class GatewayLogRepository : GenericRepository<GatewayLog>, IGatewayLogRepository
{
    private readonly ApplicationDbContext _context;

    public GatewayLogRepository(ApplicationDbContext context) : base(context)
    {
        _context = context;
    }

    public async Task<List<GatewayLog>> GetByMessageIdAsync(string messageId)
    {
        return await _context.Set<GatewayLog>()
            .Where(x => x.MessageId == messageId)
            .OrderByDescending(x => x.Timestamp)
            .ToListAsync();
    }

    public async Task<List<GatewayLog>> GetByGatewayAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Set<GatewayLog>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider);

        if (startDate.HasValue)
            query = query.Where(x => x.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(x => x.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(x => x.Timestamp)
            .ToListAsync();
    }

    public async Task<List<GatewayLog>> GetByStatusAsync(string status, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Set<GatewayLog>()
            .Where(x => x.Status == status);

        if (startDate.HasValue)
            query = query.Where(x => x.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(x => x.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(x => x.Timestamp)
            .ToListAsync();
    }

    public async Task<List<GatewayLog>> GetFailedMessagesAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        return await GetByStatusAsync("Failed", startDate, endDate);
    }

    public async Task<List<GatewayLog>> GetByRecipientAsync(string recipient, DateTime? startDate = null, DateTime? endDate = null)
    {
        var query = _context.Set<GatewayLog>()
            .Where(x => x.Recipient == recipient);

        if (startDate.HasValue)
            query = query.Where(x => x.Timestamp >= startDate.Value);

        if (endDate.HasValue)
            query = query.Where(x => x.Timestamp <= endDate.Value);

        return await query
            .OrderByDescending(x => x.Timestamp)
            .ToListAsync();
    }

    public async Task LogMessageAsync(GatewayLog log)
    {
        await _context.Set<GatewayLog>().AddAsync(log);
        await _context.SaveChangesAsync();
    }

    public async Task<Dictionary<string, long>> GetMessageCountsByStatusAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        var results = await _context.Set<GatewayLog>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider && 
                       x.Timestamp >= startDate && x.Timestamp <= endDate)
            .GroupBy(x => x.Status)
            .Select(g => new { Status = g.Key, Count = g.LongCount() })
            .ToListAsync();

        return results.ToDictionary(x => x.Status, x => x.Count);
    }

    public async Task<Dictionary<string, double>> GetLatencyStatsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        var latencies = await _context.Set<GatewayLog>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider && 
                       x.Timestamp >= startDate && x.Timestamp <= endDate && 
                       x.LatencyMs.HasValue)
            .Select(x => x.LatencyMs!.Value)
            .ToListAsync();

        if (!latencies.Any())
            return new Dictionary<string, double>();

        return new Dictionary<string, double>
        {
            ["Average"] = latencies.Average(),
            ["Min"] = latencies.Min(),
            ["Max"] = latencies.Max(),
            ["Count"] = latencies.Count
        };
    }

    public async Task<List<GatewayLog>> GetRecentLogsAsync(string gatewayName, string provider, int count = 100)
    {
        return await _context.Set<GatewayLog>()
            .Where(x => x.GatewayName == gatewayName && x.Provider == provider)
            .OrderByDescending(x => x.Timestamp)
            .Take(count)
            .ToListAsync();
    }
}
