using System;
using System.Collections.Generic;

namespace Models.DTOs.Email
{
    /// <summary>
    /// Detailed delivery report for an email message
    /// </summary>
    public class EmailDeliveryReport
    {
        /// <summary>
        /// Message identifier
        /// </summary>
        public string MessageId { get; set; } = string.Empty;

        /// <summary>
        /// Current delivery status
        /// </summary>
        public string Status { get; set; } = string.Empty;

        /// <summary>
        /// Recipient email address
        /// </summary>
        public string Recipient { get; set; } = string.Empty;

        /// <summary>
        /// Email subject
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// When the email was sent
        /// </summary>
        public DateTime? SentAt { get; set; }

        /// <summary>
        /// When the email was delivered
        /// </summary>
        public DateTime? DeliveredAt { get; set; }

        /// <summary>
        /// When the email was opened (if tracking enabled)
        /// </summary>
        public DateTime? OpenedAt { get; set; }

        /// <summary>
        /// When links were clicked (if tracking enabled)
        /// </summary>
        public DateTime? ClickedAt { get; set; }

        /// <summary>
        /// Number of times the email was opened
        /// </summary>
        public int OpenCount { get; set; }

        /// <summary>
        /// Number of times links were clicked
        /// </summary>
        public int ClickCount { get; set; }

        /// <summary>
        /// Whether the email bounced
        /// </summary>
        public bool IsBounced { get; set; }

        /// <summary>
        /// Bounce reason if applicable
        /// </summary>
        public string? BounceReason { get; set; }

        /// <summary>
        /// Whether the recipient complained/marked as spam
        /// </summary>
        public bool IsComplaint { get; set; }

        /// <summary>
        /// Whether the recipient unsubscribed
        /// </summary>
        public bool IsUnsubscribed { get; set; }

        /// <summary>
        /// Delivery events timeline
        /// </summary>
        public List<EmailEvent> Events { get; set; } = new();

        /// <summary>
        /// Gateway that sent the email
        /// </summary>
        public string? GatewayName { get; set; }

        /// <summary>
        /// Platform-specific message ID
        /// </summary>
        public string? PlatformMessageId { get; set; }

        /// <summary>
        /// Additional metadata
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Individual email delivery event
    /// </summary>
    public class EmailEvent
    {
        /// <summary>
        /// Event type (sent, delivered, opened, clicked, bounced, etc.)
        /// </summary>
        public string Type { get; set; } = string.Empty;

        /// <summary>
        /// When the event occurred
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Event description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Additional event details
        /// </summary>
        public Dictionary<string, object> Details { get; set; } = new();
    }
}
