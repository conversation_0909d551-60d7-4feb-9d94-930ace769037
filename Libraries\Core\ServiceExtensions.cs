﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.EntityFrameworkCore;
using Microsoft.OpenApi.Models;
using Models.DTOs.SMS;
using Models.DTOs.WhatsApp;
using Models.DTOs.Email;
using Services.Concrete;
using Services.Interfaces;
using System.IO;
using Data.Repos;
using SendGrid.Extensions.DependencyInjection;

namespace Core;

public static class ServiceExtensions
{
    public static void AddSharedServices(this IServiceCollection services, IConfiguration config)
    {
        // Configuration
        services.Configure<MailSettings>(config.GetSection("MailSettings"));

        // Core services
        services.AddTransient<IEmailService, EmailService>();
        services.AddTransient<Services.Interfaces.IMessageSender<SmsPayload, SmsResult>, BulkSmsService>();

        // SendGrid Email Service
        services.AddSendGrid(options =>
        {
            options.ApiKey = config["SendGridSettings:ApiKey"] ?? throw new ArgumentNullException("SendGrid API Key not configured");
        });
        services.AddTransient<Services.Interfaces.IMessageSender<EmailPayload, EmailResult>, SendGridEmailService>();

        // WhatsApp Business Service
        services.AddHttpClient<WhatsAppBusinessService>();
        services.AddTransient<IMessageService<WhatsAppPayload, WhatsAppResult>, WhatsAppBusinessService>();
    }

    public static void AddApplicationPostgreSQL(this IServiceCollection services, IConfiguration config)
    {
        services.AddDbContext<ApplicationDbContext>(options =>
        {
            options.UseNpgsql(
                config.GetConnectionString("DefaultConnection"),
                b => b.MigrationsAssembly(typeof(ApplicationDbContext).Assembly.FullName)
            );
        });

        services.AddTransient<IDbContext, ApplicationDbContext>();

        // Ensure database creation
        using var context = services.BuildServiceProvider().GetService<ApplicationDbContext>();
        context?.Database.EnsureCreated();
    }

    public static void AddRepoServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddTransient(typeof(IGenericRepository<>), typeof(GenericRepository<>));
        services.AddScoped<IUnitOfWork, UnitOfWork>();
        services.AddScoped<ILoginLogRepository, LoginLogRepository>();

        // Gateway repositories
        services.AddScoped<Data.Repos.Gateway.IGatewayConfigurationRepository, Data.Repos.Gateway.GatewayConfigurationRepository>();
        services.AddScoped<Data.Repos.Gateway.IGatewayTemplateRepository, Data.Repos.Gateway.GatewayTemplateRepository>();
        services.AddScoped<Data.Repos.Gateway.IGatewayLogRepository, Data.Repos.Gateway.GatewayLogRepository>();
        services.AddScoped<Data.Repos.Gateway.IGatewayMetricRepository, Data.Repos.Gateway.GatewayMetricRepository>();
    }

    public static void AddAppServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddTransient<ILoginLogService, LoginLogService>();

        // Gateway services
        services.AddScoped<Services.Gateway.IGatewayDataService, Services.Gateway.GatewayDataService>();
    }

    public static void AddCustomSwagger(this IServiceCollection services, IConfiguration config)
    {
        services.AddSwaggerGen(c =>
        {
            c.SwaggerDoc("v1", new OpenApiInfo
            {
                Title = "🚀 Notify Service API",
                Version = "v1.0.0",
                Description = @"# Notify Service API

A comprehensive notification service API supporting multiple communication channels with enterprise-grade features.

## 🌟 Features

### 📱 **Notification Channels**
- **Push Notifications** - FCM, APNS support
- **WhatsApp Messaging** - Business API integration
- **SMS Messaging** - Multi-provider support
- **Email Notifications** - SMTP with templates

### 🔐 **Security & Authentication**
- **JWT Authentication** - Secure token-based auth
- **Role-based Authorization** - Admin, SuperAdmin roles
- **Password Management** - Reset, forgot password flows
- **Email Verification** - Account confirmation

### 👥 **User Management**
- **User Registration** - Self-service signup
- **Admin Controls** - User management dashboard
- **Login Logging** - Audit trail and analytics
- **Role Management** - Flexible permission system

### 🔍 **Advanced Features**
- **GraphQL Support** - Flexible query interface
- **Health Monitoring** - System status endpoints
- **Redis Caching** - Performance optimization
- **PostgreSQL Database** - Reliable data storage

## 🚀 Quick Start

1. **Authenticate**: Use `/api/Account/authenticate` with your credentials
2. **Get Token**: Copy the JWT token from the response
3. **Authorize**: Click the 🔒 lock icon and enter `Bearer {your-token}`
4. **Explore**: All endpoints are now accessible!

## 📚 API Groups

- **Account** - Authentication and user management
- **Admin** - Administrative functions (requires admin role)
- **Notification** - Send notifications via various channels
- **SMS** - SMS messaging endpoints
- **Log** - Access login and audit logs
- **GraphQL** - Flexible query interface",
                Contact = new OpenApiContact
                {
                    Name = "Notify Service Team",
                    Email = "<EMAIL>",
                    Url = new Uri("https://github.com/your-org/notify-service")
                },
                License = new OpenApiLicense
                {
                    Name = "MIT License",
                    Url = new Uri("https://opensource.org/licenses/MIT")
                },
                TermsOfService = new Uri("https://notifyservice.com/terms")
            });

            // Include XML comments if the file exists
            var xmlFile = Path.Combine(AppContext.BaseDirectory, "WebApi.xml");
            if (File.Exists(xmlFile))
            {
                c.IncludeXmlComments(xmlFile, includeControllerXmlComments: true);
            }

            // Enhanced endpoint organization for Scalar UI
            c.TagActionsBy(api =>
            {
                var controllerName = api.ActionDescriptor.RouteValues["controller"];
                return new[] { GetControllerDisplayName(controllerName) };
            });

            c.DocInclusionPredicate((name, api) => true);

            // Add operation IDs for better Scalar UI experience
            c.CustomOperationIds(apiDesc =>
            {
                var controllerName = apiDesc.ActionDescriptor.RouteValues["controller"];
                var actionName = apiDesc.ActionDescriptor.RouteValues["action"];
                return $"{controllerName}_{actionName}";
            });

            // XML documentation provides comprehensive API information

            // Add server information
            c.AddServer(new OpenApiServer
            {
                Url = "https://localhost:5001",
                Description = "Development Server (HTTPS)"
            });

            c.AddServer(new OpenApiServer
            {
                Url = "http://localhost:5000",
                Description = "Development Server (HTTP)"
            });

            c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme
            {
                Description = @"JWT Authorization header using the Bearer scheme.

                **How to use:**
                1. Call `/api/Account/authenticate` with your credentials
                2. Copy the `token` from the response
                3. Click the 'Authorize' button below
                4. Enter: `Bearer {your-token-here}`
                5. Click 'Authorize'

                **Example:** `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`",
                Name = "Authorization",
                In = ParameterLocation.Header,
                Type = SecuritySchemeType.ApiKey,
                Scheme = "Bearer",
                BearerFormat = "JWT"
            });

            c.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                    new OpenApiSecurityScheme
                    {
                        Reference = new OpenApiReference
                        {
                            Type = ReferenceType.SecurityScheme,
                            Id = "Bearer"
                        },
                        Scheme = "oauth2",
                        Name = "Bearer",
                        In = ParameterLocation.Header
                    },
                    new List<string>()
                }
            });
        });
    }

    public static void AddAdditionalServices(this IServiceCollection services, IConfiguration config)
    {
        services.AddHttpClient<PushNotificationService>();
    }

    private static string GetControllerDisplayName(string controllerName)
    {
        return controllerName?.ToLower() switch
        {
            "account" => "🔐 Account Management",
            "admin" => "👑 Admin Controls",
            "notification" => "📱 Notifications",
            "sms" => "📱 SMS Messaging",
            "log" => "📊 Logging & Analytics",
            "graphql" => "🔍 GraphQL",
            _ => controllerName ?? "API"
        };
    }
}
