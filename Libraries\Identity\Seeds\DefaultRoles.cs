﻿using Identity.Models;
using Microsoft.AspNetCore.Identity;
using Models.Enums;
using System;
using System.Linq;
using System.Threading.Tasks;

namespace Identity.Seeds;

public static class DefaultRoles
{
    public static async Task SeedAsync(RoleManager<ApplicationRole> roleManager)
    {
        // Seed Roles - Check if role exists before creating
        var rolesToCreate = new[]
        {
            Roles.SuperAdmin.ToString(),
            Roles.Admin.ToString(),
            Roles.Moderator.ToString(),
            Roles.Basic.ToString()
        };

        foreach (var roleName in rolesToCreate)
        {
            if (!await roleManager.RoleExistsAsync(roleName))
            {
                var result = await roleManager.CreateAsync(new ApplicationRole()
                {
                    Name = roleName,
                    CreatedDate = DateTime.UtcNow
                });

                if (!result.Succeeded)
                {
                    // Log the error but don't throw to prevent application startup failure
                    var errors = string.Join(", ", result.Errors.Select(e => e.Description));
                    Console.WriteLine($"Failed to create role '{roleName}': {errors}");
                }
            }
        }
    }
}