#nullable enable
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading;
using System.Threading.Tasks;

namespace WebApi.Controllers;

/// <summary>
/// SMS administration endpoints
/// </summary>
[Route("api/[controller]")]
[ApiController]
[Authorize(Policy = "OnlyAdmins")]
[Produces("application/json")]
public class SmsAdminController : ControllerBase
{
    /// <summary>
    /// Get SMS service configuration
    /// </summary>
    [HttpGet("config")]
    public async Task<object> Config(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Provider = "Twilio SMS",
            AccountSid = "****" + "1234", // Masked for security
            AuthToken = "****" + "5678", // Masked
            FromNumber = "+**********",
            IsEnabled = true,
            MaxRetries = 3,
            TimeoutSeconds = 30,
            RateLimitPerMinute = 100,
            MaxMessageLength = 160,
            SupportLongMessages = true,
            LastUpdated = DateTime.UtcNow.AddDays(-1),
            Status = "Active"
        });
    }

    /// <summary>
    /// Get authentication configuration
    /// </summary>
    [HttpGet("auth")]
    public async Task<object> Auth(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            AuthType = "API Key",
            AccountSid = "****" + "1234", // Masked
            AuthToken = "****" + "5678", // Masked
            IsValid = true,
            ExpiresAt = DateTime.UtcNow.AddYears(1),
            LastValidated = DateTime.UtcNow.AddHours(-2),
            Permissions = new[] { "sms:send", "sms:read", "sms:status" }
        });
    }

    /// <summary>
    /// Get SMS templates
    /// </summary>
    [HttpGet("templates")]
    public async Task<object> Templates(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            Templates = new[]
            {
                new { Id = "verification", Name = "Verification Code", Description = "OTP verification messages" },
                new { Id = "welcome", Name = "Welcome SMS", Description = "Welcome new users" },
                new { Id = "alert", Name = "Alert Notification", Description = "Important alerts" },
                new { Id = "reminder", Name = "Appointment Reminder", Description = "Appointment reminders" }
            },
            TotalCount = 4
        });
    }

    /// <summary>
    /// Get retry configuration
    /// </summary>
    [HttpGet("retries")]
    public async Task<object> Retries(CancellationToken cancellationToken)
    {
        return await Task.FromResult(new
        {
            MaxRetries = 3,
            RetryDelaySeconds = new[] { 2, 10, 30 },
            RetryOnErrors = new[] { "TIMEOUT", "RATE_LIMIT", "TEMPORARY_FAILURE" },
            ExponentialBackoff = true,
            MaxRetryDelay = 300,
            TotalRetryTimeout = 600,
            IsEnabled = true
        });
    }

    /// <summary>
    /// Select SMS provider/gateway
    /// </summary>
    [HttpPost("provider")]
    public async Task<object> Provider([FromBody] SelectSmsProviderRequest request, CancellationToken cancellationToken)
    {
        var availableProviders = new[] { "Twilio SMS", "Nexmo (Vonage)", "BulkSMS", "Infobip" };
        
        if (!availableProviders.Contains(request.Provider))
        {
            return BadRequest(new { Error = $"Invalid provider. Available providers: {string.Join(", ", availableProviders)}" });
        }

        return await Task.FromResult(new
        {
            Success = true,
            Message = $"SMS provider switched to {request.Provider}",
            SelectedProvider = request.Provider,
            AvailableProviders = availableProviders,
            UpdatedAt = DateTime.UtcNow,
            Configuration = request.Provider switch
            {
                "Twilio SMS" => new
                {
                    Endpoint = "https://api.twilio.com/2010-04-01/Accounts/{AccountSid}/Messages.json",
                    RequiredCredentials = new[] { "AccountSid", "AuthToken", "FromNumber" },
                    Features = new[] { "SMS", "MMS", "WhatsApp", "Voice" }
                },
                "Nexmo (Vonage)" => new
                {
                    Endpoint = "https://rest.nexmo.com/sms/json",
                    RequiredCredentials = new[] { "ApiKey", "ApiSecret", "FromNumber" },
                    Features = new[] { "SMS", "Voice", "Verify", "Number Insight" }
                },
                "BulkSMS" => new
                {
                    Endpoint = "https://api.bulksms.com/v1/messages",
                    RequiredCredentials = new[] { "Username", "Password" },
                    Features = new[] { "SMS", "Bulk SMS", "Two-way SMS" }
                },
                "Infobip" => new
                {
                    Endpoint = "https://api.infobip.com/sms/2/text/advanced",
                    RequiredCredentials = new[] { "ApiKey", "BaseUrl" },
                    Features = new[] { "SMS", "Voice", "Email", "Push", "Chat Apps" }
                },
                _ => new { }
            }
        });
    }
}

/// <summary>
/// SMS provider selection request model
/// </summary>
public class SelectSmsProviderRequest
{
    public string Provider { get; set; } = string.Empty;
    public Dictionary<string, string>? Configuration { get; set; }
}
