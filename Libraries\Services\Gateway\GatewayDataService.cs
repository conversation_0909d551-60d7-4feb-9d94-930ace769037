using Data.Repos.Gateway;
using Models.DbEntities.Gateway;
using Models.DTOs.Gateway;
using Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace Services.Gateway;

public class GatewayDataService : IGatewayDataService
{
    private readonly IGatewayConfigurationRepository _configurationRepository;
    private readonly IGatewayTemplateRepository _templateRepository;
    private readonly IGatewayLogRepository _logRepository;
    private readonly IGatewayMetricRepository _metricRepository;

    public GatewayDataService(
        IGatewayConfigurationRepository configurationRepository,
        IGatewayTemplateRepository templateRepository,
        IGatewayLogRepository logRepository,
        IGatewayMetricRepository metricRepository)
    {
        _configurationRepository = configurationRepository;
        _templateRepository = templateRepository;
        _logRepository = logRepository;
        _metricRepository = metricRepository;
    }

    // Configuration Management
    public async Task<GatewayConfiguration?> GetGatewayConfigurationAsync(string gatewayName, string provider)
    {
        return await _configurationRepository.GetByGatewayNameAndProviderAsync(gatewayName, provider);
    }

    public async Task<GatewayConfiguration> SaveGatewayConfigurationAsync(string gatewayName, string gatewayType, string provider, Dictionary<string, string> configuration, string? userId = null)
    {
        var existingConfig = await _configurationRepository.GetByGatewayNameAndProviderAsync(gatewayName, provider);
        
        if (existingConfig != null)
        {
            existingConfig.ConfigurationData = JsonSerializer.Serialize(configuration);
            existingConfig.UpdatedAt = DateTime.UtcNow;
            existingConfig.UpdatedBy = userId;
            return _configurationRepository.Update(existingConfig);
        }

        var newConfig = new GatewayConfiguration
        {
            GatewayName = gatewayName,
            GatewayType = gatewayType,
            Provider = provider,
            ConfigurationData = JsonSerializer.Serialize(configuration),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            UpdatedBy = userId,
            IsEnabled = true
        };

        return _configurationRepository.Insert(newConfig);
    }

    public async Task<bool> UpdateGatewayConfigurationAsync(int configurationId, Dictionary<string, string> configuration, string? userId = null)
    {
        var config = _configurationRepository.GetById(configurationId);
        if (config == null) return false;

        config.ConfigurationData = JsonSerializer.Serialize(configuration);
        config.UpdatedAt = DateTime.UtcNow;
        config.UpdatedBy = userId;
        
        return _configurationRepository.Update(config) != null;
    }

    public async Task<List<GatewayConfiguration>> GetGatewaysByTypeAsync(string gatewayType)
    {
        return await _configurationRepository.GetByGatewayTypeAsync(gatewayType);
    }

    public async Task<bool> SetDefaultGatewayAsync(int configurationId, string gatewayType)
    {
        return await _configurationRepository.SetDefaultGatewayAsync(configurationId, gatewayType);
    }

    // Template Management
    public async Task<List<Models.DTOs.Gateway.GatewayTemplate>> GetTemplatesAsync(int gatewayConfigurationId)
    {
        var dbTemplates = await _templateRepository.GetByGatewayConfigurationIdAsync(gatewayConfigurationId);
        return dbTemplates.Select(ConvertToInterfaceTemplate).ToList();
    }

    public async Task<Models.DTOs.Gateway.GatewayTemplate> SaveTemplateAsync(int gatewayConfigurationId, Models.DTOs.Gateway.GatewayTemplate template, string? userId = null)
    {
        var existingTemplate = await _templateRepository.GetByTemplateIdAsync(gatewayConfigurationId, template.TemplateId);
        
        if (existingTemplate != null)
        {
            existingTemplate.Name = template.Name;
            existingTemplate.Subject = template.Subject;
            existingTemplate.Content = template.Content;
            existingTemplate.Variables = JsonSerializer.Serialize(template.Variables);
            existingTemplate.Tags = JsonSerializer.Serialize(template.Tags);
            existingTemplate.UpdatedAt = DateTime.UtcNow;
            existingTemplate.UpdatedBy = userId;
            existingTemplate.IsActive = template.IsActive;
            var updatedTemplate = _templateRepository.Update(existingTemplate);
            return ConvertToInterfaceTemplate(updatedTemplate);
        }

        var newTemplate = new Models.DbEntities.Gateway.GatewayTemplate
        {
            GatewayConfigurationId = gatewayConfigurationId,
            TemplateId = template.TemplateId,
            Name = template.Name,
            Subject = template.Subject,
            Content = template.Content,
            Variables = JsonSerializer.Serialize(template.Variables),
            Tags = JsonSerializer.Serialize(template.Tags),
            CreatedAt = DateTime.UtcNow,
            UpdatedAt = DateTime.UtcNow,
            CreatedBy = userId,
            UpdatedBy = userId,
            IsActive = template.IsActive
        };

        var savedTemplate = _templateRepository.Insert(newTemplate);
        return ConvertToInterfaceTemplate(savedTemplate);
    }

    public async Task<bool> DeleteTemplateAsync(int gatewayConfigurationId, string templateId)
    {
        return await _templateRepository.DeleteByTemplateIdAsync(gatewayConfigurationId, templateId);
    }

    public async Task<Models.DTOs.Gateway.GatewayTemplate?> GetTemplateAsync(int gatewayConfigurationId, string templateId)
    {
        var dbTemplate = await _templateRepository.GetByTemplateIdAsync(gatewayConfigurationId, templateId);
        return dbTemplate != null ? ConvertToInterfaceTemplate(dbTemplate) : null;
    }

    // Logging
    public async Task LogMessageAsync(string messageId, string gatewayName, string provider, string operation, string status, 
        object? requestPayload = null, object? responseData = null, string? errorMessage = null, 
        string? errorCode = null, double? latencyMs = null, string? recipient = null, 
        string? subject = null, string? messageType = null, int retryCount = 0, 
        Dictionary<string, object>? additionalInfo = null, int? gatewayConfigurationId = null)
    {
        // Get gateway configuration ID if not provided
        if (!gatewayConfigurationId.HasValue)
        {
            var config = await GetGatewayConfigurationAsync(gatewayName, provider);
            gatewayConfigurationId = config?.Id;
        }

        var log = new GatewayLog
        {
            MessageId = messageId,
            GatewayName = gatewayName,
            Provider = provider,
            Operation = operation,
            Status = status,
            RequestPayload = requestPayload != null ? JsonSerializer.Serialize(requestPayload) : null,
            ResponseData = responseData != null ? JsonSerializer.Serialize(responseData) : null,
            ErrorMessage = errorMessage,
            ErrorCode = errorCode,
            LatencyMs = latencyMs,
            Recipient = recipient,
            Subject = subject,
            MessageType = messageType,
            RetryCount = retryCount,
            AdditionalInfo = additionalInfo != null ? JsonSerializer.Serialize(additionalInfo) : null,
            Timestamp = DateTime.UtcNow,
            GatewayConfigurationId = gatewayConfigurationId ?? 0
        };

        await _logRepository.LogMessageAsync(log);
    }

    public async Task<List<GatewayLog>> GetMessageLogsAsync(string messageId)
    {
        return await _logRepository.GetByMessageIdAsync(messageId);
    }

    public async Task<List<GatewayLog>> GetGatewayLogsAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null)
    {
        return await _logRepository.GetByGatewayAsync(gatewayName, provider, startDate, endDate);
    }

    public async Task<List<GatewayLog>> GetFailedMessagesAsync(DateTime? startDate = null, DateTime? endDate = null)
    {
        return await _logRepository.GetFailedMessagesAsync(startDate, endDate);
    }

    // Metrics
    public async Task SaveMetricAsync(string gatewayName, string provider, string metricType, string period, 
        DateTime periodStart, DateTime periodEnd, long totalMessages = 0, long successfulMessages = 0, 
        long failedMessages = 0, double averageLatencyMs = 0, Dictionary<string, object>? metricData = null, 
        int? gatewayConfigurationId = null)
    {
        // Get gateway configuration ID if not provided
        if (!gatewayConfigurationId.HasValue)
        {
            var config = await GetGatewayConfigurationAsync(gatewayName, provider);
            gatewayConfigurationId = config?.Id;
        }

        var existingMetric = await _metricRepository.GetMetricAsync(gatewayName, provider, metricType, period, periodStart);
        
        if (existingMetric != null)
        {
            existingMetric.TotalMessages = totalMessages;
            existingMetric.SuccessfulMessages = successfulMessages;
            existingMetric.FailedMessages = failedMessages;
            existingMetric.SuccessRate = totalMessages > 0 ? (double)successfulMessages / totalMessages * 100 : 0;
            existingMetric.FailureRate = totalMessages > 0 ? (double)failedMessages / totalMessages * 100 : 0;
            existingMetric.AverageLatencyMs = averageLatencyMs;
            existingMetric.MetricData = metricData != null ? JsonSerializer.Serialize(metricData) : null;
            existingMetric.UpdatedAt = DateTime.UtcNow;
            _metricRepository.Update(existingMetric);
        }
        else
        {
            var metric = new GatewayMetric
            {
                GatewayName = gatewayName,
                Provider = provider,
                MetricType = metricType,
                Period = period,
                PeriodStart = periodStart,
                PeriodEnd = periodEnd,
                TotalMessages = totalMessages,
                SuccessfulMessages = successfulMessages,
                FailedMessages = failedMessages,
                SuccessRate = totalMessages > 0 ? (double)successfulMessages / totalMessages * 100 : 0,
                FailureRate = totalMessages > 0 ? (double)failedMessages / totalMessages * 100 : 0,
                AverageLatencyMs = averageLatencyMs,
                MetricData = metricData != null ? JsonSerializer.Serialize(metricData) : null,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                GatewayConfigurationId = gatewayConfigurationId ?? 0
            };
            await _metricRepository.SaveMetricAsync(metric);
        }
    }

    public async Task<List<GatewayMetric>> GetMetricsAsync(string gatewayName, string provider, DateTime? startDate = null, DateTime? endDate = null)
    {
        return await _metricRepository.GetByGatewayAsync(gatewayName, provider, startDate, endDate);
    }

    public async Task<Dictionary<string, object>> GetAggregatedMetricsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        return await _metricRepository.GetAggregatedMetricsAsync(gatewayName, provider, startDate, endDate);
    }

    // Real-time data for gateway interfaces
    public async Task<GatewayConfigurationResult> GetConfigurationResultAsync(string gatewayName, string provider)
    {
        var config = await GetGatewayConfigurationAsync(gatewayName, provider);
        if (config == null)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = "Gateway configuration not found",
                ErrorCode = "CONFIG_NOT_FOUND"
            };
        }

        var configData = JsonSerializer.Deserialize<Dictionary<string, string>>(config.ConfigurationData) ?? new Dictionary<string, string>();

        return new GatewayConfigurationResult
        {
            IsSuccess = true,
            Message = "Configuration retrieved from database",
            Configuration = configData,
            RequiredFields = GetRequiredFields(gatewayName, provider),
            OptionalFields = GetOptionalFields(gatewayName, provider)
        };
    }

    public async Task<GatewayConfigurationResult> UpdateConfigurationResultAsync(string gatewayName, string provider, Dictionary<string, string> configuration, string? userId = null)
    {
        try
        {
            var config = await GetGatewayConfigurationAsync(gatewayName, provider);
            if (config != null)
            {
                await UpdateGatewayConfigurationAsync(config.Id, configuration, userId);
            }
            else
            {
                await SaveGatewayConfigurationAsync(gatewayName, GetGatewayType(gatewayName), provider, configuration, userId);
            }

            return new GatewayConfigurationResult
            {
                IsSuccess = true,
                Message = "Configuration updated in database",
                Configuration = configuration,
                UpdatedAt = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            return new GatewayConfigurationResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "UPDATE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplatesResult> GetTemplatesResultAsync(string gatewayName, string provider)
    {
        var config = await GetGatewayConfigurationAsync(gatewayName, provider);
        if (config == null)
        {
            return new GatewayTemplatesResult
            {
                IsSuccess = false,
                ErrorMessage = "Gateway configuration not found",
                ErrorCode = "CONFIG_NOT_FOUND"
            };
        }

        var templates = await GetTemplatesAsync(config.Id);
        var gatewayTemplates = templates.Select(t => new Models.DTOs.Gateway.GatewayTemplate
        {
            Id = t.TemplateId,
            Name = t.Name,
            Description = t.Description,
            Content = t.Content,
            ContentType = t.ContentType,
            Variables = JsonSerializer.Deserialize<Dictionary<string, string>>(t.Variables) ?? new Dictionary<string, string>(),
            Tags = JsonSerializer.Deserialize<string[]>(t.Tags) ?? Array.Empty<string>(),
            CreatedAt = t.CreatedAt,
            UpdatedAt = t.UpdatedAt,
            IsActive = t.IsActive
        }).ToArray();

        return new GatewayTemplatesResult
        {
            IsSuccess = true,
            Templates = gatewayTemplates,
            TotalCount = gatewayTemplates.Length
        };
    }

    public async Task<GatewayTemplateResult> SaveTemplateResultAsync(string gatewayName, string provider, Models.DTOs.Gateway.GatewayTemplate template, string? userId = null)
    {
        try
        {
            var config = await GetGatewayConfigurationAsync(gatewayName, provider);
            if (config == null)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Gateway configuration not found",
                    ErrorCode = "CONFIG_NOT_FOUND"
                };
            }

            var dbTemplate = new GatewayTemplate
            {
                TemplateId = template.Id,
                Name = template.Name,
                Description = template.Description,
                Content = template.Content,
                ContentType = template.ContentType,
                Variables = template.Variables,
                Tags = template.Tags
            };

            var savedTemplate = await SaveTemplateAsync(config.Id, dbTemplate, userId);

            return new GatewayTemplateResult
            {
                IsSuccess = true,
                Message = $"Template '{template.Name}' saved to database",
                Template = template
            };
        }
        catch (Exception ex)
        {
            return new GatewayTemplateResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "SAVE_FAILED"
            };
        }
    }

    public async Task<GatewayTemplateResult> DeleteTemplateResultAsync(string gatewayName, string provider, string templateId)
    {
        try
        {
            var config = await GetGatewayConfigurationAsync(gatewayName, provider);
            if (config == null)
            {
                return new GatewayTemplateResult
                {
                    IsSuccess = false,
                    ErrorMessage = "Gateway configuration not found",
                    ErrorCode = "CONFIG_NOT_FOUND"
                };
            }

            var success = await DeleteTemplateAsync(config.Id, templateId);

            return new GatewayTemplateResult
            {
                IsSuccess = success,
                Message = success ? $"Template '{templateId}' deleted from database" : "Template not found",
                ErrorCode = success ? null : "TEMPLATE_NOT_FOUND"
            };
        }
        catch (Exception ex)
        {
            return new GatewayTemplateResult
            {
                IsSuccess = false,
                ErrorMessage = ex.Message,
                ErrorCode = "DELETE_FAILED"
            };
        }
    }

    // Analytics and reporting
    public async Task<Dictionary<string, long>> GetMessageCountsByStatusAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        return await _logRepository.GetMessageCountsByStatusAsync(gatewayName, provider, startDate, endDate);
    }

    public async Task<Dictionary<string, double>> GetLatencyStatsAsync(string gatewayName, string provider, DateTime startDate, DateTime endDate)
    {
        return await _logRepository.GetLatencyStatsAsync(gatewayName, provider, startDate, endDate);
    }

    public async Task<List<GatewayLog>> GetRecentLogsAsync(string gatewayName, string provider, int count = 100)
    {
        return await _logRepository.GetRecentLogsAsync(gatewayName, provider, count);
    }

    // Helper methods
    private string[] GetRequiredFields(string gatewayName, string provider)
    {
        return provider.ToLower() switch
        {
            "fcm" => new[] { "ServerKey", "ProjectId" },
            "onesignal" => new[] { "AppId", "ApiKey" },
            "twilio" => new[] { "AccountSid", "AuthToken" },
            "nexmo" => new[] { "ApiKey", "ApiSecret" },
            "sendgrid" => new[] { "ApiKey" },
            "mailgun" => new[] { "ApiKey", "Domain" },
            "smtp" => new[] { "Host", "Port", "Username", "Password" },
            "whatsapp" => new[] { "AccessToken", "PhoneNumberId" },
            "telegram" => new[] { "BotToken" },
            "facebook" => new[] { "AccessToken", "PageId" },
            _ => Array.Empty<string>()
        };
    }

    private string[] GetOptionalFields(string gatewayName, string provider)
    {
        return provider.ToLower() switch
        {
            "smtp" => new[] { "EnableSsl", "DisplayName" },
            "twilio" => new[] { "FromNumber" },
            "sendgrid" => new[] { "FromEmail", "FromName" },
            "mailgun" => new[] { "FromEmail", "FromName" },
            _ => Array.Empty<string>()
        };
    }

    private string GetGatewayType(string gatewayName)
    {
        return gatewayName.ToLower() switch
        {
            "push" => "Push",
            "sms" => "SMS",
            "email" => "Email",
            "messengerapp" => "MessengerApp",
            "webhook" => "Webhook",
            _ => "Unknown"
        };
    }

    private Models.DTOs.Gateway.GatewayTemplate ConvertToInterfaceTemplate(Models.DbEntities.Gateway.GatewayTemplate dbTemplate)
    {
        var variables = string.IsNullOrEmpty(dbTemplate.Variables)
            ? new Dictionary<string, string>()
            : JsonSerializer.Deserialize<Dictionary<string, string>>(dbTemplate.Variables) ?? new Dictionary<string, string>();

        var tags = string.IsNullOrEmpty(dbTemplate.Tags)
            ? new List<string>()
            : JsonSerializer.Deserialize<List<string>>(dbTemplate.Tags) ?? new List<string>();

        return new Models.DTOs.Gateway.GatewayTemplate
        {
            TemplateId = dbTemplate.TemplateId,
            Name = dbTemplate.Name,
            Subject = dbTemplate.Subject,
            Content = dbTemplate.Content,
            Variables = variables,
            Tags = tags,
            IsActive = dbTemplate.IsActive
        };
    }
}
