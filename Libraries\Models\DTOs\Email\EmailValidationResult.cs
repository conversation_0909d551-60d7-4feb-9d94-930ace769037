using System;
using System.Collections.Generic;

namespace Models.DTOs.Email
{
    /// <summary>
    /// Result of email address validation
    /// </summary>
    public class EmailValidationResult
    {
        /// <summary>
        /// The email address that was validated
        /// </summary>
        public string Email { get; set; } = string.Empty;

        /// <summary>
        /// Whether the email address is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// Confidence score (0-100) if available
        /// </summary>
        public int? ConfidenceScore { get; set; }

        /// <summary>
        /// Whether the email is deliverable
        /// </summary>
        public bool? IsDeliverable { get; set; }

        /// <summary>
        /// Whether the email is a disposable/temporary email
        /// </summary>
        public bool? IsDisposable { get; set; }

        /// <summary>
        /// Whether the email is a role-based email (admin@, support@, etc.)
        /// </summary>
        public bool? IsRoleBased { get; set; }

        /// <summary>
        /// Domain information
        /// </summary>
        public string? Domain { get; set; }

        /// <summary>
        /// Local part of the email (before @)
        /// </summary>
        public string? LocalPart { get; set; }

        /// <summary>
        /// Detailed validation information
        /// </summary>
        public Dictionary<string, object> ValidationDetails { get; set; } = new();

        /// <summary>
        /// Error message if validation failed
        /// </summary>
        public string? ErrorMessage { get; set; }

        /// <summary>
        /// When the validation was performed
        /// </summary>
        public DateTime ValidatedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// Gateway that performed the validation
        /// </summary>
        public string? GatewayName { get; set; }

        /// <summary>
        /// Raw validation response from the provider
        /// </summary>
        public object? ProviderResponse { get; set; }
    }
}
